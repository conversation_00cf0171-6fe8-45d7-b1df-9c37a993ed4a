const User = require('../models/User');

/**
 * Creates a default admin user if it doesn't exist
 */
async function createDefaultAdmin() {
  try {
    // Check if any admin user already exists in the database
    const adminExists = await User.findOne({ role: 'admin' });
    
    if (!adminExists) {
      // Create default admin user
      const adminUser = await User.create({
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        mobile: '+919664774890',
        role: 'admin',
        isVerified: true,
        status: 1
      });

      console.log('✅ Default admin user created successfully');
    } else {
      console.log('ℹ️ Admin user already exists - skipping default admin creation');
    }
  } catch (error) {
    console.error('❌ Error creating default admin user:', error.message);
  }
}

module.exports = createDefaultAdmin; 