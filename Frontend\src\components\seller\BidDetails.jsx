import React, { useState, useEffect } from "react";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getBid } from "../../redux/slices/bidSlice";
import SellerLayout from "./SellerLayout";
import Table from "../common/Table";
import LoadingSkeleton from "../common/LoadingSkeleton";
import { ErrorDisplay } from "../common/ErrorBoundary";
import "../../styles/BidDetails.css";
import { FiEye } from "react-icons/fi";
import {
  getImageUrl,
  getPlaceholderImage,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth
} from "../../utils/constants";
import { FaEye, FaCheck, FaTimes, FaSpinner } from "react-icons/fa";
import { formatTableDate, formatStandardDate } from "../../utils/dateValidation";

const BidDetails = () => {
  const { id } = useParams();
  const dispatch = useDispatch();
  const { bid, isLoading, isError, error } = useSelector((state) => state.bid);
  const [bidHistory, setBidHistory] = useState([]);

  useEffect(() => {
    if (id) {
      dispatch(getBid(id));
    }
  }, [dispatch, id]);

  useEffect(() => {
    if (bid) {
      // Create history data - for now using single bid, but in future this could include bidding history
      const history = [
        {
          bidId: bid._id,
          customer: `${bid.bidder?.firstName || ""} ${bid.bidder?.lastName || ""}`.trim() || "Unknown",
          date: new Date(bid.createdAt).toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          }),
          price: bid.content?.price ? `$${parseFloat(bid.content.price).toFixed(2)}` : "N/A",
          bidAmount: `$${parseFloat(bid.amount).toFixed(2)}`,
          status: bid.status || "Pending",

        },
      ];
      setBidHistory(history);
    }
  }, [bid]);

  const historyColumns = [
    { key: "bidId", label: "Bid Id" },
    { key: "customer", label: "Customer" },
    { key: "date", label: "Date" },
    { key: "price", label: "Price" },
    { key: "bidAmount", label: "Bid Amount" },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <span
          className={`status-badge status-${item.status
            .toLowerCase()
            .replace(" ", "-")}`}
        >
          {item.status}
        </span>
      ),
    },
  ];

  if (isLoading) {
    return (
      <SellerLayout>
        <div className="BidDetails">
          <LoadingSkeleton type="table" rows={1} />
        </div>
      </SellerLayout>
    );
  }

  if (isError || !bid) {
    return (
      <SellerLayout>
        <div className="BidDetails">
          <ErrorDisplay
            title="Error Loading Bid Details"
            message={error?.message || "Failed to load bid details"}
            onRetry={() => dispatch(getBid(id))}
          />
        </div>
      </SellerLayout>
    );
  }

  const formatBidData = (bids) => {
    return bids.map((bid, index) => ({
      no: index + 1,
      bidId: `#${bid._id.slice(-6)}`,
      date: formatTableDate(bid.createdAt),
      bidAmount: `$${bid.amount.toFixed(2)}`,
      bidder: bid.bidder?.firstName || 'Anonymous',
      status: bid.status,
      _id: bid._id,
      rawBid: bid
    }));
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatTime = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-US", {
      hour: "2-digit",
      minute: "2-digit",
      hour12: true,
    });
  };

  return (
    <SellerLayout>
      <div className="BidDetails">
        <div className="BidDetails__content">
          <div className="BidDetails__main-section">
            <div className="BidDetails__header">
              <div className="BidDetails__content-info">
                <img
                  src={bid.content?._id
                    ? getProxyUrlWithAuth(getProxyThumbnailUrl(bid.content._id))
                    : bid.content?.thumbnailUrl
                      ? getImageUrl(bid.content.thumbnailUrl)
                      : getPlaceholderImage(200, 120, "No image")
                  }
                  alt={bid.content?.title || "Content"}
                  className="BidDetails__content-image"
                  onError={(e) => {
                    e.target.src = getPlaceholderImage(200, 120, "Image not found");
                  }}
                />
                <div className="BidDetails__content-details">
                  <h3 className="BidDetails__content-title">{bid.content?.title || "Untitled Content"}</h3>
                  <p className="BidDetails__content-subtitle">{bid.content?.sport || "Sports Content"}</p>
                </div>
              </div>
            </div>

            <div className="BidDetails__info-grid">
              <div className="BidDetails__info-section">
                <div className="BidDetails__info-item-grid">
                  <h3 className="BidDetails__section-title">Bid Information</h3>
                  <div>
                    <div className="BidDetails__info-item">
                      <span className="BidDetails__info-label">Bid Id</span>
                      <span className="BidDetails__info-value">#{bid._id?.substring(0, 8)}</span>
                    </div>
                    <div className="BidDetails__info-item">
                      <span className="BidDetails__info-label">Date</span>
                      <span className="BidDetails__info-value">
                        {formatDate(bid.createdAt)} | {formatTime(bid.createdAt)}
                      </span>
                    </div>
                    <div className="vertical-line"></div>
                  </div>
                  <div>
                    <div className="BidDetails__info-item">
                      <span className="BidDetails__info-label">Price</span>
                      <span className="BidDetails__info-value">
                        {bid.content?.price ? `$${parseFloat(bid.content.price).toFixed(2)}` : "N/A"}
                      </span>
                    </div>
                    <div className="BidDetails__info-item">
                      <span className="BidDetails__info-label">Bid Amount</span>
                      <span className="BidDetails__info-value">
                        ${parseFloat(bid.amount).toFixed(2)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="vertical-line"></div>

                <div className="BidDetails__info-item-grid">
                  <h3 className="BidDetails__section-title">
                    Customer Details
                  </h3>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Name</span>
                    <span className="BidDetails__info-value">
                      {`${bid.bidder?.firstName || ""} ${bid.bidder?.lastName || ""}`.trim() || "Unknown Customer"}
                    </span>
                  </div>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">
                      Email Address
                    </span>
                    <span className="BidDetails__info-value">
                      {bid.bidder?.email || "N/A"}
                    </span>
                  </div>
                  <div className="BidDetails__info-item">
                    <span className="BidDetails__info-label">Phone Number</span>
                    <span className="BidDetails__info-value">
                      {bid.bidder?.phone || "N/A"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>



          <div className="BidDetails__history-section">
            <h3 className="BidDetails__section-title">Bid History</h3>
            <Table
              columns={historyColumns}
              data={bidHistory}
              className="BidDetails__history-table"
            />
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default BidDetails;
