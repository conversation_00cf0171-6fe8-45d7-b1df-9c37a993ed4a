import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useLocation, Navigate } from 'react-router-dom';
import { 
  selectIsRestricted, 
  selectRestrictionReason,
  selectMaintenanceMode,
  selectLaunchDateTime 
} from '../../redux/slices/settingsSlice';
import authService from '../../services/authService';

/**
 * LaunchStatusGuard Component
 * Checks if the site is in restricted mode and redirects non-admin users
 * to the countdown/maintenance page
 */
const LaunchStatusGuard = ({ children }) => {
  const location = useLocation();
  const isRestricted = useSelector(selectIsRestricted);
  const restrictionReason = useSelector(selectRestrictionReason);
  const maintenanceMode = useSelector(selectMaintenanceMode);
  const launchDateTime = useSelector(selectLaunchDateTime);

  // Get current user data
  const userData = authService.getStoredUser();
  const isAuthenticated = authService.isAuthenticated();

  // Check if current user is admin
  const isAdmin = userData?.role === 'admin';

  // Routes that should always be accessible even during restrictions
  const allowedPaths = [
    '/launch-countdown',
    '/auth',
    '/signup', 
    '/otp-verification'
  ];

  // Check if current path is allowed during restrictions
  const isAllowedPath = allowedPaths.some(path => 
    location.pathname.startsWith(path)
  );

  // If site is restricted and user is not admin and not on allowed path
  if (isRestricted && !isAdmin && !isAllowedPath) {
    return <Navigate to="/launch-countdown" replace />;
  }

  // If user is on countdown page but site is not restricted (and they're not admin)
  if (location.pathname === '/launch-countdown' && !isRestricted && !isAdmin) {
    return <Navigate to="/" replace />;
  }

  return children;
};

export default LaunchStatusGuard;
