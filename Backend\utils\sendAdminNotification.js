const User = require('../models/User');
const sendEmail = require('./sendEmail');

/**
 * Send email notification to all admin users
 * @param {Object} options - Email options
 * @param {string} options.subject - Email subject
 * @param {string} options.message - Plain text message
 * @param {string} options.html - HTML message
 * @returns {Promise} Promise with email send results
 */
const sendAdminNotification = async (options) => {
  try {
    console.log('📧 Sending notification to all admin users...');
    
    // Get all admin users
    const adminUsers = await User.find({ 
      role: 'admin',
      status: 1 // Only active admin users
    }).select('email firstName lastName');

    if (adminUsers.length === 0) {
      console.log('⚠️ No admin users found to send notification to');
      return { success: false, message: 'No admin users found' };
    }

    console.log(`📧 Found ${adminUsers.length} admin users to notify`);

    // Send email to each admin user
    const emailPromises = adminUsers.map(async (admin) => {
      try {
        await sendEmail({
          to: admin.email,
          subject: options.subject,
          message: options.message,
          html: options.html
        });
        console.log(`✅ Email sent successfully to admin: ${admin.email}`);
        return { success: true, email: admin.email };
      } catch (error) {
        console.error(`❌ Failed to send email to admin ${admin.email}:`, error);
        return { success: false, email: admin.email, error: error.message };
      }
    });

    // Wait for all emails to be sent
    const results = await Promise.allSettled(emailPromises);
    
    const successful = results.filter(result => 
      result.status === 'fulfilled' && result.value.success
    ).length;
    
    const failed = results.length - successful;

    console.log(`📧 Email notification results: ${successful} successful, ${failed} failed`);

    return {
      success: true,
      totalAdmins: adminUsers.length,
      successful,
      failed,
      results: results.map(result => 
        result.status === 'fulfilled' ? result.value : { success: false, error: result.reason }
      )
    };

  } catch (error) {
    console.error('❌ Error sending admin notifications:', error);
    throw new Error(`Failed to send admin notifications: ${error.message}`);
  }
};

module.exports = sendAdminNotification;
