import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import AdminLayout from "../../components/admin/AdminLayout";
import "../../styles/AdminRequestManagement.css";
import { FaSearch, FaFilter, FaEye, FaEdit, FaTrash, FaCheck, FaTimes } from "react-icons/fa";

const AdminRequestManagement = () => {
    const dispatch = useDispatch();
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");

    return (
        <AdminLayout>
            <div className="AdminRequestManagement">
                <div className="AdminRequestManagement__header">

                    <p>Manage and monitor all custom training requests</p>
                </div>

                {/* Search and Filter Section */}
                <div className="AdminRequestManagement__controls">
                    <div className="search-box">
                        <FaSearch className="search-icon" />
                        <input
                            type="text"
                            placeholder="Search requests..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>

                    <div className="filter-box">

                        <select
                            value={statusFilter}
                            onChange={(e) => setStatusFilter(e.target.value)}
                        >
                            <option value="all">All Status</option>
                            <option value="pending">Pending</option>
                            <option value="approved">Approved</option>
                            <option value="rejected">Rejected</option>
                            <option value="completed">Completed</option>
                        </select>
                    </div>
                </div>

                {/* Table Section */}
                <div className="AdminRequestManagement__table">
                    <table>
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" />
                                </th>
                                <th>Request ID</th>
                                <th>Title</th>
                                <th>Buyer</th>
                                <th>Sport</th>
                                <th>Budget</th>
                                <th>Status</th>
                                <th>Created Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {/* Table rows will be populated here */}
                            <tr>
                                <td colSpan="9" className="no-data">
                                    Loading requests...
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                {/* Pagination */}
                <div className="AdminRequestManagement__pagination">
                    <div className="pagination-info">
                        Showing 0 of 0 requests
                    </div>
                    <div className="pagination-controls">
                        <button className="btn btn-outline" disabled>
                            Previous
                        </button>
                        <span className="page-number active">1</span>
                        <button className="btn btn-outline" disabled>
                            Next
                        </button>
                    </div>
                </div>
            </div>
        </AdminLayout>
    );
};

export default AdminRequestManagement; 