import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { showSuccess, showError, showInfo } from '../../utils/toast';
import { createConnectAccount, getConnectAccountStatus, createDashboardLink } from '../../services/paymentService';
import { getCurrentUser } from '../../redux/slices/authSlice';
import { usePlatformCommission } from '../../hooks/useSettings';
import SellerLayout from '../../components/seller/SellerLayout';
import '../../styles/SellerSettings.css';

// Icons
import { MdPayment, MdCheckCircle, MdError, MdRefresh, MdDashboard } from 'react-icons/md';
import { FaStripe, FaExternalLinkAlt } from 'react-icons/fa';

const SellerSettings = () => {
  const { user } = useSelector((state) => state.auth);
  const dispatch = useDispatch();
  const [stripeStatus, setStripeStatus] = useState({
    connected: false,
    accountId: '',
    detailsSubmitted: false,
    chargesEnabled: false,
    payoutsEnabled: false,
    loading: true,
    error: null
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Use platform commission hook
  const {
    platformCommission,
    sellerEarnings,
    loading: commissionLoading
  } = usePlatformCommission();

  useEffect(() => {
    checkStripeStatus();
  }, []);

  // Refresh Stripe status when user's stripeConnectId changes
  useEffect(() => {
    if (user?.paymentInfo?.stripeConnectId) {
      checkStripeStatus();
    }
  }, [user?.paymentInfo?.stripeConnectId]);



  const checkStripeStatus = async () => {
    try {
      setStripeStatus(prev => ({ ...prev, loading: true, error: null }));

      const stripeConnectId = user?.paymentInfo?.stripeConnectId;

      if (!stripeConnectId) {
        setStripeStatus(prev => ({
          ...prev,
          connected: false,
          loading: false
        }));
        return;
      }

      const response = await getConnectAccountStatus(stripeConnectId);

      if (response.success) {
        setStripeStatus(prev => ({
          ...prev,
          connected: true,
          accountId: response.data.accountId,
          detailsSubmitted: response.data.detailsSubmitted,
          chargesEnabled: response.data.chargesEnabled,
          payoutsEnabled: response.data.payoutsEnabled,
          loading: false
        }));
      } else {
        throw new Error(response.message || 'Failed to get Stripe status');
      }

    } catch (error) {
      console.error('Error checking Stripe status:', error);
      setStripeStatus(prev => ({
        ...prev,
        loading: false,
        error: error.message
      }));
    }
  };

  const handleStripeSetup = async () => {
    try {
      setIsRefreshing(true);

      const response = await createConnectAccount({
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        context: 'settings'
      });

      if (response.success) {
        // Open Stripe onboarding in a new window
        const stripeWindow = window.open(
          response.data.onboardingUrl,
          'stripe-onboarding',
          'width=800,height=600,scrollbars=yes,resizable=yes'
        );

        // Poll for completion
        const pollInterval = setInterval(async () => {
          if (stripeWindow.closed) {
            clearInterval(pollInterval);
            setIsRefreshing(false);
            // Refresh user data first to get updated stripeConnectId
            await dispatch(getCurrentUser());
            // Then refresh Stripe status
            await checkStripeStatus();
          }
        }, 2000);

        // Show appropriate message based on account status
        const message = response.data.isExisting
          ? 'Continue your Stripe account setup in the opened window.'
          : 'Stripe onboarding window opened. Please complete the setup.';

        showSuccess(message);

      } else {
        throw new Error(response.message || 'Failed to setup Stripe Connect');
      }

    } catch (error) {
      console.error('Stripe setup error:', error);
      setIsRefreshing(false);
      showError(error.message || 'Failed to setup Stripe Connect');
    }
  };

  const handleDashboardAccess = async () => {
    try {
      const response = await createDashboardLink();

      if (response.success) {
        // Open Stripe dashboard in a new window
        window.open(
          response.data.dashboardUrl,
          'stripe-dashboard',
          'width=1200,height=800,scrollbars=yes,resizable=yes'
        );

        // Show message if provided (for fallback cases)
        if (response.data.message) {
          showInfo(response.data.message);
        }
      } else {
        throw new Error(response.message || 'Failed to access dashboard');
      }
    } catch (error) {
      console.error('Dashboard access error:', error);

      // For 500 errors, try opening the general Stripe dashboard as fallback
      if (error.response?.status === 500 || error.message.includes('Failed to create dashboard link')) {
        window.open(
          'https://dashboard.stripe.com/dashboard',
          'stripe-dashboard',
          'width=1200,height=800,scrollbars=yes,resizable=yes'
        );
        showInfo('Opening general Stripe dashboard. Please log in to your Stripe account.');
      } else {
        showError(error.message || 'Failed to access Stripe dashboard');
      }
    }
  };

  const getStatusIcon = (enabled) => {
    return enabled ? (
      <MdCheckCircle className="status-icon success" />
    ) : (
      <MdError className="status-icon error" />
    );
  };

  return (
    <SellerLayout>
      <div className="seller-settings-content">
        {/* Stripe Connect Status */}
        <div className="settings-section">
          <div className="section-header">
            <div className="section-title">
              <FaStripe className="section-icon" />
              <h2>Stripe Standard Account</h2>
            </div>
            {!stripeStatus.loading && stripeStatus.connected && (
              <button
                className="btn-outline refresh-btn"
                onClick={checkStripeStatus}
                disabled={isRefreshing}
              >
                <MdRefresh className={isRefreshing ? 'spinning' : ''} />
                Refresh Status
              </button>
            )}
          </div>

          <div className="stripe-status-card">
            {stripeStatus.loading ? (
              <div className="loading-state">
                <div className="spinner"></div>
                <p>Checking Stripe Standard account status...</p>
              </div>
            ) : stripeStatus.error ? (
              <div className="error-state">
                <MdError className="error-icon" />
                <div className="error-content">
                  <h3>Error Loading Status</h3>
                  <p>{stripeStatus.error}</p>
                  <button className="btn btn-primary" onClick={checkStripeStatus}>
                    Try Again
                  </button>
                </div>
              </div>
            ) : !stripeStatus.connected ? (
              <div className="not-connected-state">
                <div className="setup-prompt">
                  <h3>🏦 Fast & Secure Payment Setup Required</h3>
                  <p>Connect your Stripe Express account to receive payments from your content sales.</p>
                  <div className="benefits-list">
                    <div className="benefit-item">
                      <MdCheckCircle className="benefit-icon" />
                      <span>Stripe Express account with streamlined setup</span>
                    </div>
                    <div className="benefit-item">
                      <MdCheckCircle className="benefit-icon" />
                      <span>Quick onboarding process</span>
                    </div>
                    <div className="benefit-item">
                      <MdCheckCircle className="benefit-icon" />
                      <span>
                        You keep {commissionLoading ? '...' : `${sellerEarnings}%`} of each sale
                      </span>
                    </div>
                    <div className="benefit-item">
                      <MdCheckCircle className="benefit-icon" />
                      <span>Automatic tax handling and reporting</span>
                    </div>
                    <div className="benefit-item">
                      <MdCheckCircle className="benefit-icon" />
                      <span>Built-in fraud protection</span>
                    </div>
                  </div>
                  <button
                    className="btn btn-stripe-connect"
                    onClick={handleStripeSetup}
                    disabled={isRefreshing}
                  >
                    {isRefreshing ? 'Setting up...' : '🔒 Setup Stripe Express Account'}
                  </button>
                </div>
              </div>
            ) : (
              <div className="connected-state">
                <div className="status-overview">
                  <div className="status-header">
                    <MdCheckCircle className="main-status-icon success" />
                    <div>
                      <h3>Stripe Express Account Connected</h3>
                      <p>Account ID: {stripeStatus.accountId}</p>
                    </div>
                  </div>

                  <div className="status-grid">
                    <div className="status-item">
                      {getStatusIcon(stripeStatus.detailsSubmitted)}
                      <div className="status-content">
                        <h4>Account Details</h4>
                        <p>{stripeStatus.detailsSubmitted ? 'Complete' : 'Incomplete'}</p>
                      </div>
                    </div>
                    <div className="status-item">
                      {getStatusIcon(stripeStatus.chargesEnabled)}
                      <div className="status-content">
                        <h4>Accept Payments</h4>
                        <p>{stripeStatus.chargesEnabled ? 'Enabled' : 'Disabled'}</p>
                      </div>
                    </div>
                    <div className="status-item">
                      {getStatusIcon(stripeStatus.payoutsEnabled)}
                      <div className="status-content">
                        <h4>Receive Payouts</h4>
                        <p>{stripeStatus.payoutsEnabled ? 'Enabled' : 'Disabled'}</p>
                      </div>
                    </div>
                  </div>

                  {(!stripeStatus.detailsSubmitted || !stripeStatus.chargesEnabled || !stripeStatus.payoutsEnabled) && (
                    <div className="incomplete-warning">
                      <MdError className="warning-icon" />
                      <div className="warning-content">
                        <h4>Setup Incomplete</h4>
                        <p>Please complete your Stripe onboarding to start receiving payments.</p>
                        <button
                          className="btn btn-primary"
                          onClick={handleStripeSetup}
                          disabled={isRefreshing}
                        >
                          Complete Setup
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Dashboard Access for Standard Accounts */}
                  {stripeStatus.detailsSubmitted && stripeStatus.chargesEnabled && (
                    <div className="dashboard-access">
                      <div className="dashboard-info">
                        <MdDashboard className="dashboard-icon" />
                        <div>
                          <h4>Access Your Stripe Dashboard</h4>
                          <p>View detailed analytics, manage payouts, configure payout schedules, and access all Stripe features.</p>
                        </div>
                      </div>
                      <button
                        className="btn btn-primary dashboard-btn"
                        onClick={handleDashboardAccess}
                      >
                        <MdDashboard />
                        Open Stripe Dashboard
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Payment Information */}
        <div className="settings-section">
          <div className="section-header">
            <div className="section-title">
              <MdPayment className="section-icon" />
              <h2>Payment Information</h2>
            </div>
          </div>

          <div className="payment-info-grid">
            <div className="info-card">
              <h3>Platform Fee</h3>
              <div className="fee-display">
                {commissionLoading ? (
                  <div className="loading-spinner">...</div>
                ) : (
                  `${platformCommission}%`
                )}
              </div>
              <p>Deducted from each sale</p>
            </div>
            <div className="info-card">
              <h3>Your Earnings</h3>
              <div className="fee-display">
                {commissionLoading ? (
                  <div className="loading-spinner">...</div>
                ) : (
                  `${sellerEarnings}%`
                )}
              </div>
              <p>Automatically transferred to your account</p>
            </div>
            <div className="info-card">
              <h3>Payout Schedule</h3>
              <div className="fee-display">Daily</div>
              <p>Express payouts available</p>
            </div>
          </div>
        </div>

        {/* Help Section */}
        <div className="settings-section">
          <div className="help-card">
            <h3>Need Help?</h3>
            <p>Having trouble with your payment setup? Contact our support team for assistance.</p>
            <div className="help-links">
              <a href="/contact" className="help-link">
                Contact Support <FaExternalLinkAlt />
              </a>
              <a href="https://stripe.com/docs/connect" target="_blank" rel="noopener noreferrer" className="help-link">
                Stripe Documentation <FaExternalLinkAlt />
              </a>
            </div>
          </div>
        </div>
      </div>
    </SellerLayout>
  );
};

export default SellerSettings;