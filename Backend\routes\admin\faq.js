const express = require('express');
const { check } = require('express-validator');
const {
  getAllFAQs,
  getFAQById,
  createFAQ,
  updateFAQ,
  deleteFAQ,
  bulkDeleteFAQs,
  updateFAQStatus,
  reorderFAQs
} = require('../../controllers/admin/faq');

const { protect, authorize } = require('../../middleware/auth');

const router = express.Router();

// Apply authentication and authorization middleware
router.use(protect);
router.use(authorize('admin'));

// Get all FAQs with filtering, pagination, and search
router.get('/', getAllFAQs);

// Create new FAQ
router.post('/', [
  check('question', 'Question is required and must be between 1-500 characters')
    .isLength({ min: 1, max: 500 })
    .trim(),
  check('answer', 'Answer is required and must be between 1-2000 characters')
    .isLength({ min: 1, max: 2000 })
    .trim(),
  check('category', 'Category must be between 1-100 characters')
    .optional({ nullable: true, checkFalsy: true })
    .isLength({ min: 1, max: 100 })
    .trim(),
  check('status', 'Status must be either active or inactive')
    .optional({ nullable: true, checkFalsy: true })
    .isIn(['active', 'inactive']),
  check('order', 'Order must be a non-negative number')
    .optional({ nullable: true, checkFalsy: true })
    .isInt({ min: 0 })
], createFAQ);

// Bulk operations
router.post('/bulk-delete', [
  check('faqIds', 'FAQ IDs array is required')
    .isArray({ min: 1 })
    .withMessage('At least one FAQ ID is required'),
  check('faqIds.*', 'Each FAQ ID must be a valid MongoDB ObjectId')
    .isMongoId()
], bulkDeleteFAQs);

// Reorder FAQs
router.put('/reorder', [
  check('faqOrders', 'FAQ orders array is required')
    .isArray({ min: 1 })
    .withMessage('At least one FAQ order is required'),
  check('faqOrders.*.id', 'Each FAQ ID must be a valid MongoDB ObjectId')
    .isMongoId(),
  check('faqOrders.*.order', 'Each order must be a non-negative number')
    .isInt({ min: 0 })
], reorderFAQs);

// Individual FAQ operations
router.get('/:id', [
  check('id', 'FAQ ID must be a valid MongoDB ObjectId').isMongoId()
], getFAQById);

router.put('/:id', [
  check('id', 'FAQ ID must be a valid MongoDB ObjectId').isMongoId(),
  check('question', 'Question is required and must be between 1-500 characters')
    .isLength({ min: 1, max: 500 })
    .trim(),
  check('answer', 'Answer is required and must be between 1-2000 characters')
    .isLength({ min: 1, max: 2000 })
    .trim(),
  check('category', 'Category must be between 1-100 characters')
    .optional({ nullable: true, checkFalsy: true })
    .isLength({ min: 1, max: 100 })
    .trim(),
  check('status', 'Status must be either active or inactive')
    .optional({ nullable: true, checkFalsy: true })
    .isIn(['active', 'inactive']),
  check('order', 'Order must be a non-negative number')
    .optional({ nullable: true, checkFalsy: true })
    .isInt({ min: 0 })
], updateFAQ);

router.delete('/:id', [
  check('id', 'FAQ ID must be a valid MongoDB ObjectId').isMongoId()
], deleteFAQ);

// Update FAQ status
router.patch('/:id/status', [
  check('id', 'FAQ ID must be a valid MongoDB ObjectId').isMongoId(),
  check('status', 'Status is required and must be either active or inactive')
    .isIn(['active', 'inactive'])
], updateFAQStatus);

module.exports = router;
