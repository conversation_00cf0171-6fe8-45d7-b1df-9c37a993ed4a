const sendEmail = require('./sendEmail');

/**
 * Send OTP via Email using <PERSON><PERSON><PERSON>er
 * @param {string} email - Email address to send OTP to
 * @param {string} otp - OTP code to send
 * @returns {Promise} Promise with email send response
 */
const sendEmailOTP = async (email, otp) => {
  try {
    await sendEmail({
      email,
      subject: 'Your XO Sports Hub Verification Code',
      message: `Your verification code is: ${otp}. This code is valid for 10 minutes.`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #333;">XO Sports Hub Verification</h2>
          <p>Your verification code is:</p>
          <div style="background-color: #f4f4f4; padding: 15px; text-align: center; font-size: 24px; letter-spacing: 5px; font-weight: bold;">
            ${otp}
          </div>
          <p>This code is valid for 10 minutes.</p>
          <p>If you didn't request this code, please ignore this email.</p>
        </div>
      `
    });

    return true;
  } catch (error) {
    console.error('Error sending Email OTP:', error);
    throw new Error('Failed to send Email OTP');
  }
};

/**
 * Send OTP via Email only
 * @param {Object} user - User object with email
 * @param {string} otp - OTP code to send
 * @returns {Promise} Promise with email send result
 */
const sendOTP = async (user, otp) => {
  const results = {
    email: null,
    success: false,
    errors: []
  };

  // Check if email is provided
  if (!user.email) {
    console.error('No email address provided for OTP');
    throw new Error('No email address provided');
  }

  try {
    // Send OTP via email
    const emailResult = await sendEmailOTP(user.email, otp);

    results.email = emailResult;
    results.success = true;

    return results;
  } catch (error) {
    console.error('Error sending email OTP:', error);
    results.errors.push(`Email: ${error.message}`);
    throw new Error(`Failed to send OTP: ${error.message}`);
  }
};

module.exports = {
  sendOTP,
  sendEmailOTP
};