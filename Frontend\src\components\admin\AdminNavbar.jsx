import React, { useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { selectProfile } from "../../redux/slices/adminDashboardSlice";
import { logout } from "../../redux/slices/authSlice";
import "../../styles/AdminNavbar.css";
import { IMAGE_BASE_URL } from "../../utils/constants";
import { usePublicSettings } from "../../hooks/useSettings";

// Logo
import XOSportsLogo from "../../assets/images/XOsports-hub-logo.png";

// Icons
import { IoMdNotifications } from "react-icons/io";
import { FaUser, FaCog, FaBars } from "react-icons/fa";
import { IoLogOut } from "react-icons/io5";

const AdminNavbar = ({ onToggleSidebar }) => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const profile = useSelector(selectProfile);
  const { getSiteLogoUrl, siteName } = usePublicSettings();

  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);

  // Mock notification count
  const notificationCount = 5;

  // Handle logout
  const handleLogout = () => {
    dispatch(logout());
    navigate("/");
  };

  // Handle profile navigation
  const handleProfileClick = () => {
    navigate("/admin/profile");
    setShowProfileDropdown(false);
  };



  return (
    <div className="AdminNavbar">
      <div className="AdminNavbar__container">
        {/* Left Section - Sidebar Toggle & Logo */}
        <div className="AdminNavbar__left">
          <button
            className="AdminNavbar__toggle"
            onClick={onToggleSidebar}
            aria-label="Toggle Sidebar"
          >
            <FaBars />
          </button>

          <div className="AdminNavbar__logo">
            <img
              src={getSiteLogoUrl() || XOSportsLogo}
              alt={siteName || "XO Sports Hub Logo"}
            />
          </div>
        </div>

        {/* Right Section - Notifications & Profile */}
        <div className="AdminNavbar__right">

          {/* Admin Profile */}
          <div className="AdminNavbar__profile">
            <button
              className="profile-btn"
              onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              aria-label="Admin Profile"
            >
              <div className="profile-avatar">
                {profile.profileImage ? (
                  <img src={IMAGE_BASE_URL + profile.profileImage} alt="Admin" />
                ) : (
                  <FaUser />
                )}
              </div>
              <div className="profile-info">
                <span className="profile-name">
                  {profile.firstName} {profile.lastName}
                </span>
                <span className="profile-role">Administrator</span>
              </div>
            </button>

            {showProfileDropdown && (
              <div className="profile-dropdown">
                <div className="profile-dropdown-header">
                  <div className="profile-avatar large">
                    {profile.profileImage ? (
                      <img src={IMAGE_BASE_URL + profile.profileImage} alt="Admin" />
                    ) : (
                      <FaUser />
                    )}
                  </div>
                  <div className="profile-details">
                    <h4>{profile.firstName} {profile.lastName}</h4>
                    <p>{profile.email}</p>
                  </div>
                </div>
                <div className="profile-dropdown-menu">
                  <button
                    className="dropdown-item"
                    onClick={handleProfileClick}
                  >
                    <FaUser className="dropdown-icon" />
                    View Profile
                  </button>

                  <hr className="dropdown-divider" />
                  <button
                    className="dropdown-item logout"
                    onClick={handleLogout}
                  >
                    <IoLogOut className="dropdown-icon" />
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Overlay for closing dropdowns */}
      {(showProfileDropdown || showNotifications) && (
        <div
          className="AdminNavbar__overlay"
          onClick={() => {
            setShowProfileDropdown(false);
            setShowNotifications(false);
          }}
        />
      )}
    </div>
  );
};

export default AdminNavbar;
