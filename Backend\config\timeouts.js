// Payment and auction timeout configurations
// Environment-based payment timeouts

// Get payment timeout based on environment
const getPaymentTimeoutConfig = () => {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
        // Development: 10 minutes
        return {
            PAYMENT_DEADLINE_HOURS: 0.17, // 10 minutes
            PAYMENT_DEADLINE_MS: 10 * 60 * 1000, // 10 minutes in milliseconds
        };
    } else {
        // Production: 24 hours
        return {
            PAYMENT_DEADLINE_HOURS: 24, // 24 hours
            PAYMENT_DEADLINE_MS: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
        };
    }
};

const paymentConfig = getPaymentTimeoutConfig();

const timeouts = {
    // Payment deadlines - environment-based
    PAYMENT_DEADLINE_HOURS: paymentConfig.PAYMENT_DEADLINE_HOURS,
    PAYMENT_DEADLINE_MS: paymentConfig.PAYMENT_DEADLINE_MS,

    // Background job intervals - faster for testing
    ORDER_CLEANUP_INTERVAL: '*/2 * * * *', // every 2 minutes
    RUNNER_UP_NOTIFICATION_INTERVAL: '*/1 * * * *', // every 1 minute
    // FRAUD_DETECTION_INTERVAL removed - fraud detection disabled

    // File upload timeouts
    UPLOAD_TIMEOUT_MS: 3600 * 1000, // 1 hour for large video uploads
    UPLOAD_TIMEOUT_SECONDS: 3600, // 1 hour in seconds

    // Other timeouts
    SESSION_TIMEOUT: 5 * 60 * 1000, // 5 minutes

    // Auction extensions
    AUCTION_EXTENSION_HOURS: 0.5, // 30 minutes
    AUCTION_EXTENSION_MS: 30 * 60 * 1000, // 30 minutes in milliseconds

    // Auction conversion interval
    AUCTION_CONVERSION_INTERVAL: '*/5 * * * *' // Every 5 minutes - check for expired auctions
};

// Helper functions
const getPaymentDeadline = (fromDate = new Date()) => {
    return new Date(fromDate.getTime() + timeouts.PAYMENT_DEADLINE_MS);
};

// Log current payment timeout configuration
console.log(`Payment timeout configured for ${process.env.NODE_ENV || 'unknown'} environment: ${timeouts.PAYMENT_DEADLINE_HOURS} hours (${timeouts.PAYMENT_DEADLINE_MS / 1000 / 60} minutes)`);
console.log(`Payment expiration emails: Only sent for auction bids and seller-accepted offers`);

const getOrderExpiryTime = (fromDate = new Date()) => {
    return new Date(fromDate.getTime() + timeouts.PAYMENT_DEADLINE_MS);
};

const getAuctionExtension = (fromDate = new Date()) => {
    return new Date(fromDate.getTime() + timeouts.AUCTION_EXTENSION_MS);
};

const formatTimeRemaining = (deadline) => {
    const now = new Date();
    const remaining = deadline - now;

    if (remaining <= 0) return 'Expired';

    const minutes = Math.floor(remaining / (1000 * 60));
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    return `${minutes}m`;
};

module.exports = {
    ...timeouts,
    getPaymentDeadline,
    getOrderExpiryTime,
    getAuctionExtension,
    formatTimeRemaining,
}; 