import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { fetchPublicSettings, fetchPlatformCommission } from '../../redux/slices/settingsSlice';

/**
 * Settings Provider Component
 * Initializes global settings on app startup
 * Should be placed high in the component tree to ensure settings are available
 */
const SettingsProvider = ({ children }) => {
  const dispatch = useDispatch();

  useEffect(() => {
    // Fetch settings on app initialization
    dispatch(fetchPublicSettings());
    dispatch(fetchPlatformCommission());
  }, [dispatch]);

  return children;
};

export default SettingsProvider;
