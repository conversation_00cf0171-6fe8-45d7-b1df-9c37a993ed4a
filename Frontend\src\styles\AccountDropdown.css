.account-dropdown {
  position: relative;
  display: inline-block;
}

.account-dropdown .account-dropdown__button {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  background-color: transparent;
  color: var(--btn-color);
  border: none;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-weight: 500;

  font-size: var(--basefont);
  transition: all 0.3s ease;
}

.account-dropdown .account-dropdown__user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 2px;
}

.account-dropdown .account-dropdown__user-name {
  font-size: var(--basefont);
  font-weight: 600;
  line-height: 1.2;
}

.account-dropdown .account-dropdown__user-role {
  font-size: calc(var(--basefont) - 2px);
  font-weight: 400;
  opacity: 0.8;
  line-height: 1;
}

.account-dropdown .account-dropdown__user-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.account-dropdown .account-dropdown__user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: var(--white);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.account-dropdown .account-dropdown__user-icon {
  font-size: 14px;
  color: var(--white);
}

.account-dropdown .account-dropdown__icon {
  font-size: var(--basefont);
  transition: transform 0.3s ease;
}

.account-dropdown .account-dropdown__button.active .account-dropdown__icon {
  transform: rotate(180deg);
}

.account-dropdown .account-dropdown__menu {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 200px;
  background-color: var(--white);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--smallfont) 0;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  overflow: hidden;
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s linear 0.3s;
}

.account-dropdown .account-dropdown__menu.active {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease, visibility 0s linear;
}

.account-dropdown .account-dropdown__item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px var(--basefont);
  color: var(--secondary-color) !important;
  text-decoration: none;
  font-size: var(--basefont);
  transition: background-color 0.3s ease;
}

.account-dropdown .account-dropdown__item:hover {
  background-color: var(--bg-blue);
  color: var(--btn-color) !important;
}

.account-dropdown .account-dropdown__item.active {
  color: var(--btn-color);
  font-weight: 600;
}

.account-dropdown .account-dropdown__item-icon {
  font-size: var(--basefont);
  color: currentColor;
}

.account-dropdown .account-dropdown__divider {
  height: 1px;
  background-color: var(--light-gray);
  margin: 8px 0;
}

/* Responsive styles */
@media (max-width: 768px) {
  .account-dropdown {
    display: none;
  }

  /* Show dropdown in mobile sidebar */
  .sidebar-component .account-dropdown {
    display: block;
    width: 100%;
  }

  .sidebar-component .account-dropdown .account-dropdown__button {
    width: 100%;
    justify-content: center;
  }

  .sidebar-component .account-dropdown .account-dropdown__menu {
    position: static;
    width: 100%;
    box-shadow: none;
    border: 1px solid var(--light-gray);
    margin-top: 8px;
  }

  .sidebar-component .account-dropdown .account-dropdown__menu.active {
    display: block;
  }
}
