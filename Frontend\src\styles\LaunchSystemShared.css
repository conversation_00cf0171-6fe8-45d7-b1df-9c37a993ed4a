/* Shared Design System for Launch Countdown & Maintenance Pages */

/* Common Layout Components */
.launch-system-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Poppins', sans-serif;
  transition: all 0.5s ease-in-out;
}

.launch-system-container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 900px;
  padding: 2rem;
  text-align: center;
}

.launch-system-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-large);
  padding: 3rem 2rem;
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: fadeInUp 0.8s ease-out;
}

.launch-system-header {
  margin-bottom: 2.5rem;
  animation: slideInFromLeft 0.8s ease-out 0.2s both;
}

.launch-system-logo {
  max-height: 80px;
  width: auto;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.launch-system-title {
  font-size: var(--heading2);
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

/* Icon Groups */
.launch-system-icon-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.launch-system-icon {
  font-size: 2.5rem;
  animation: system-pulse 2s ease-in-out infinite;
}

.launch-system-icon--primary {
  animation-delay: 0s;
}

.launch-system-icon--secondary {
  animation-delay: 0.3s;
}

.launch-system-icon--tertiary {
  animation-delay: 0.6s;
}

/* Message Components */
.launch-system-message {
  font-size: var(--body-text-large);
  color: var(--text-color);
  margin-bottom: 1rem;
  line-height: 1.6;
  animation: slideInFromRight 0.8s ease-out 0.4s both;
}

.launch-system-submessage {
  font-size: var(--body-text);
  color: var(--text-color-light);
  margin-bottom: 2rem;
  line-height: 1.5;
  animation: slideInFromRight 0.8s ease-out 0.6s both;
}

/* Status Components */
.launch-system-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid;
}

.launch-system-status--warning {
  background: rgba(255, 107, 107, 0.1);
  border-color: rgba(255, 107, 107, 0.2);
}

.launch-system-status--info {
  background: rgba(0, 123, 255, 0.1);
  border-color: rgba(0, 123, 255, 0.2);
}

.launch-system-status-icon {
  font-size: 1.25rem;
}

.launch-system-status-icon--warning {
  color: #ff6b6b;
}

.launch-system-status-icon--info {
  color: #007bff;
}

.launch-system-status-text {
  font-weight: 600;
  margin: 0;
  font-size: var(--body-text);
}

.launch-system-status-text--warning {
  color: #d63031;
}

.launch-system-status-text--info {
  color: #007bff;
}

/* Social Links */
.launch-system-social {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 2rem;
  animation: fadeInUp 0.8s ease-out 0.8s both;
}

.launch-system-social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.launch-system-social-link:hover {
  background: var(--primary-hover);
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Admin Login Button */
.launch-system-admin-login {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  animation: fadeInUp 0.8s ease-out 1s both;
}

.launch-system-admin-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--small-text);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.launch-system-admin-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.launch-system-admin-btn:active {
  transform: translateY(0);
}

.launch-system-admin-btn svg {
  font-size: 1rem;
}

/* Background Components */
.launch-system-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}



/* Floating Elements */
.launch-system-floating-icons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.launch-system-floating-icon {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 2rem;
  animation: float 6s ease-in-out infinite;
}

/* Animations */
@keyframes system-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes background-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(10px) rotate(-5deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .launch-system-container {
    padding: 1rem;
  }

  .launch-system-content {
    padding: 2rem 1.5rem;
  }

  .launch-system-title {
    font-size: var(--heading3);
  }

  .launch-system-icon-group {
    gap: 0.75rem;
  }

  .launch-system-icon {
    font-size: 2rem;
  }

  .launch-system-message {
    font-size: var(--body-text);
  }

  .launch-system-social {
    gap: 0.75rem;
  }

  .launch-system-social-link {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .launch-system-admin-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .launch-system-floating-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .launch-system-title {
    font-size: var(--heading4);
  }

  .launch-system-icon {
    font-size: 1.75rem;
  }

  .launch-system-status {
    flex-direction: column;
    gap: 0.5rem;
  }

  .launch-system-social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .launch-system-floating-icon {
    font-size: 1.25rem;
  }
}
