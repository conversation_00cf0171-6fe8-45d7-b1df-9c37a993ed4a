import api from './api';

/**
 * Settings Service
 * Handles platform settings API calls
 */

// Get platform commission from database
export const getPlatformCommission = async () => {
    try {
        const response = await api.get('/settings/platform-commission');
        return response.data;
    } catch (error) {
        console.error('Error fetching platform commission:', error);
        throw error;
    }
};

const settingsService = {
    getPlatformCommission,
};

export default settingsService; 