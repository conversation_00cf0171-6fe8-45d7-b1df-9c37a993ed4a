import React from 'react';
import { useSelector } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  selectSiteName,
  selectSiteLogo
} from '../redux/slices/settingsSlice';
import { IMAGE_BASE_URL } from '../utils/constants';
import authService from '../services/authService';
import '../styles/MaintenancePage.css';

// Icons
import {
  FaTools,
  FaWrench,
  FaCog,
  FaUserShield,
  FaExclamationTriangle
} from 'react-icons/fa';

const MaintenancePage = () => {
  const navigate = useNavigate();
  const siteName = useSelector(selectSiteName);
  const siteLogo = useSelector(selectSiteLogo);

  // Check if user is already logged in as admin
  const userData = authService.getStoredUser();
  const isAdmin = userData?.role === 'admin';

  // Handle admin login redirect
  const handleAdminLogin = () => {
    navigate('/auth?admin=true');
  };

  // Get site logo URL
  const getSiteLogoUrl = () => {
    if (!siteLogo) return null;
    if (siteLogo.startsWith('http')) return siteLogo;
    return `${IMAGE_BASE_URL || 'http://localhost:5000'}${siteLogo}`;
  };

  const logoUrl = getSiteLogoUrl();

  return (
    <div className="maintenance-page">
      <div className="maintenance-page__container">
        <div className="maintenance-page__content">
          <div className="maintenance-page__header">
            {logoUrl && (
              <img 
                src={logoUrl} 
                alt={siteName} 
                className="maintenance-page__logo"
              />
            )}
            <div className="maintenance-page__icon-group">
              <FaTools className="maintenance-page__icon maintenance-page__icon--primary" />
              <FaWrench className="maintenance-page__icon maintenance-page__icon--secondary" />
              <FaCog className="maintenance-page__icon maintenance-page__icon--tertiary" />
            </div>
            <h1 className="maintenance-page__title">
              Under Maintenance
            </h1>
          </div>

          <div className="maintenance-page__maintenance">
            <div className="maintenance-page__status">
              <FaExclamationTriangle className="maintenance-page__status-icon" />
              <p className="maintenance-page__status-text">
                Site Temporarily Unavailable
              </p>
            </div>

            <p className="maintenance-page__message">
              We're currently performing scheduled maintenance to improve your experience on {siteName}.
            </p>
            
            <p className="maintenance-page__submessage">
              Our team is working hard to get everything back online as quickly as possible.
            </p>



            <div className="maintenance-page__features">
              <h3 className="maintenance-page__features-title">What we're improving:</h3>
              <ul className="maintenance-page__features-list">
                <li>Enhanced performance and speed</li>
                <li>New features and functionality</li>
                <li>Security updates and improvements</li>
                <li>Better user experience</li>
              </ul>
            </div>
          </div>



          {/* Admin Login Button */}
          {!isAdmin && (
            <div className="maintenance-page__admin-login">
              <button
                onClick={handleAdminLogin}
                className="admin-login-btn"
                title="Admin Login"
                type="button"
              >
                <FaUserShield />
                <span>Admin Login</span>
              </button>
            </div>
          )}
        </div>

        <div className="maintenance-page__background">
          {/* <div className="background-animation"></div> */}
          <div className="maintenance-page__floating-icons">
            <FaTools className="floating-icon floating-icon--1" />
            <FaWrench className="floating-icon floating-icon--2" />
            <FaCog className="floating-icon floating-icon--3" />
            <FaTools className="floating-icon floating-icon--4" />
            <FaWrench className="floating-icon floating-icon--5" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MaintenancePage;
