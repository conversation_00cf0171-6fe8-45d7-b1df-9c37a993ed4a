// Phone validation utilities for backend

// Only allow India (+91) and USA (+1)
const ALLOWED_COUNTRY_CODES = ['+91', '+1'];

/**
 * Validate phone number format and country code
 * @param {string} mobile - Full mobile number with country code
 * @returns {Object} - Validation result with isValid and error message
 */
const validateMobileNumber = (mobile) => {
  if (!mobile || typeof mobile !== 'string') {
    return {
      isValid: false,
      error: 'Mobile number is required'
    };
  }

  // Remove any spaces, dashes, or other non-digit characters except +
  const cleanMobile = mobile.replace(/[^\d+]/g, '');

  // Check if it starts with an allowed country code
  let countryCode = null;
  let phoneNumber = null;

  if (cleanMobile.startsWith('+91')) {
    countryCode = '+91';
    phoneNumber = cleanMobile.slice(3);
  } else if (cleanMobile.startsWith('+1')) {
    countryCode = '+1';
    phoneNumber = cleanMobile.slice(2);
  } else if (cleanMobile.startsWith('91') && cleanMobile.length === 12) {
    // Handle case where +91 is entered as 91
    countryCode = '+91';
    phoneNumber = cleanMobile.slice(2);
  } else if (cleanMobile.startsWith('1') && cleanMobile.length === 11) {
    // Handle case where +1 is entered as 1
    countryCode = '+1';
    phoneNumber = cleanMobile.slice(1);
  } else {
    return {
      isValid: false,
      error: 'Only India (+91) and USA (+1) phone numbers are allowed'
    };
  }

  // Validate phone number length and format based on country
  if (countryCode === '+91') {
    // India: 10 digits, should start with 6, 7, 8, or 9
    if (phoneNumber.length !== 10) {
      return {
        isValid: false,
        error: 'Indian phone number must be 10 digits'
      };
    }
    if (!/^[6-9]/.test(phoneNumber)) {
      return {
        isValid: false,
        error: 'Invalid Indian phone number format'
      };
    }
  } else if (countryCode === '+1') {
    // USA: 10 digits, area code cannot start with 0 or 1
    if (phoneNumber.length !== 10) {
      return {
        isValid: false,
        error: 'US phone number must be 10 digits'
      };
    }
    if (/^[01]/.test(phoneNumber)) {
      return {
        isValid: false,
        error: 'Invalid US phone number format'
      };
    }
  }

  return {
    isValid: true,
    error: null,
    countryCode,
    phoneNumber,
    formattedMobile: `${countryCode}${phoneNumber}`
  };
};

/**
 * Normalize mobile number to standard format
 * @param {string} mobile - Mobile number
 * @returns {string} - Normalized mobile number
 */
const normalizeMobileNumber = (mobile) => {
  const validation = validateMobileNumber(mobile);
  if (validation.isValid) {
    return validation.formattedMobile;
  }
  return mobile; // Return original if invalid (let validation handle the error)
};

/**
 * Check if country code is allowed
 * @param {string} countryCode - Country code to check
 * @returns {boolean} - Whether the country code is allowed
 */
const isAllowedCountryCode = (countryCode) => {
  return ALLOWED_COUNTRY_CODES.includes(countryCode);
};

module.exports = {
  validateMobileNumber,
  normalizeMobileNumber,
  isAllowedCountryCode,
  ALLOWED_COUNTRY_CODES
};
