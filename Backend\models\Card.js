const mongoose = require('mongoose');

const CardSchema = new mongoose.Schema({
  user: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  stripePaymentMethodId: {
    type: String,
    required: [true, 'Stripe payment method ID is required'],
    unique: true
  },
  lastFourDigits: {
    type: String,
    required: [true, 'Last four digits are required'],
    length: 4
  },
  cardType: {
    type: String,
    required: [true, 'Card type is required'],
    enum: ['visa', 'mastercard', 'amex', 'discover', 'diners', 'jcb', 'unionpay', 'unknown']
  },
  expiryMonth: {
    type: Number,
    required: [true, 'Expiry month is required'],
    min: 1,
    max: 12
  },
  expiryYear: {
    type: Number,
    required: [true, 'Expiry year is required'],
    min: new Date().getFullYear()
  },
  cardholderName: {
    type: String,
    required: [true, 'Cardholder name is required'],
    trim: true
  },
  isDefault: {
    type: Boolean,
    default: false
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isReusable: {
    type: Boolean,
    default: true,
    description: 'Whether this payment method can be reused for future payments'
  },
  billingAddress: {
    line1: String,
    line2: String,
    city: String,
    state: String,
    postalCode: String,
    country: String
  },
  fingerprint: {
    type: String,
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field before saving
CardSchema.pre('save', function (next) {
  this.updatedAt = Date.now();
  next();
});

// Ensure only one default card per user
CardSchema.pre('save', async function (next) {
  if (this.isDefault && this.isModified('isDefault')) {
    // Remove default status from other cards of the same user
    await this.constructor.updateMany(
      { user: this.user, _id: { $ne: this._id } },
      { isDefault: false }
    );
  }
  next();
});

// Index for efficient queries
CardSchema.index({ user: 1, isActive: 1 });
CardSchema.index({ user: 1, isDefault: 1 });
CardSchema.index({ stripePaymentMethodId: 1 });

module.exports = mongoose.model('Card', CardSchema);
