const express = require('express');
const { check } = require('express-validator');
const {
  getActiveFAQs,
  getFAQCategories,
  searchFAQs
} = require('../controllers/faq');

const router = express.Router();

// Public routes - no authentication required

// Get all active FAQs
router.get('/', getActiveFAQs);

// Get FAQ categories
router.get('/categories', getFAQCategories);

// Search FAQs
router.get('/search', [
  check('q', 'Search term must be at least 2 characters long')
    .isLength({ min: 2 })
    .trim(),
  check('category', 'Category must be between 1-100 characters')
    .optional({ nullable: true, checkFalsy: true })
    .isLength({ min: 1, max: 100 })
    .trim(),
  check('limit', 'Limit must be a positive number between 1-100')
    .optional({ nullable: true, checkFalsy: true })
    .isInt({ min: 1, max: 100 })
], searchFAQs);

module.exports = router;
