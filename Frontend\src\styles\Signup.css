/**/
/* Signup Page Styles */
.signup__page {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: calc(100vh - var(--navbar-height, 90px));
  padding: var(--spacing-lg, 40px) var(--spacing-md, 20px);
  background-color: var(--bg-gray);
}

.signup__page .signup__container {
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  box-shadow: var(--box-shadow-light);
  padding: var(--spacing-lg, 40px);
  width: 100%;
  max-width: var(--container-sm, 500px);
}

.signup__page .signup__title {
  font-size: var(--heading4);
  font-weight: 600;
  color: var(--secondary-color);
  text-align: center;
  margin-bottom: var(--spacing-md, 20px);
}

/* Account Type Selection */
.signup__page .signup__account-type {
  margin-bottom: var(--spacing-md, 20px);
}

.signup__page .signup__label {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__page .signup__options {
  display: flex;
  gap: var(--spacing-sm, 15px);
  justify-content: center;
}

.signup__page .signup__option {
  display: flex;
  align-items: flex-start;
  padding: var(--spacing-sm, 15px);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  width: 50%;
  gap: var(--spacing-xs, 10px);
  position: relative;
}

.signup__page .signup__option--selected {
  border-color: var(--btn-color);
  background-color: rgba(238, 52, 37, 0.05);
}

.signup__page .signup__option-checkbox {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: 2px solid var(--light-gray);
  border-radius: 50%;
  margin-top: 2px;
  transition: all 0.3s ease;
  position: absolute;
  top: 5px;
  right: 5px;
}

.signup__page .signup__option--selected .signup__option-checkbox {
  border-color: var(--btn-color);
  background-color: var(--btn-color);
}

.signup__page .signup__option-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.signup__page .signup__check-icon {
  color: var(--white);
  font-size: 10px;
}

.signup__page .signup__option-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.signup__page .signup__option-icon {
  font-size: var(--heading5);
  color: var(--secondary-color);
  margin-bottom: var(--spacing-xs, 10px);
}

.signup__page .signup__option-text {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  text-align: center;
}

/* Form Styles */
.signup__page .signup__form {
  display: flex;
  flex-direction: column;
}

.signup__page .signup__form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm, 15px);
}

.signup__page .signup__input-container {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md, 20px);
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-medium);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.signup__page .signup__input-container:focus-within {
  border-color: var(--btn-color);

}

.signup__page .signup__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: var(--spacing-xs, 10px);
  color: var(--dark-gray);
  font-size: var(--heading6);
  min-width: var(--spacing-lg, 40px);
}

.signup__page .signup__input {
  width: 100%;
  padding: var(--spacing-xs, 10px) var(--spacing-sm, 15px);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
 border-top-right-radius: var(--border-radius-medium);
border-bottom-right-radius: var(--border-radius-medium);
}

.signup__page .signup__input:focus {
  border-color: var(--btn-color);

  outline: none;
}

.signup__page .signup__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.signup__page .signup__input--error {
  border-color: #ff3b30;
}

.signup__page .signup__error {
  color: #ff3b30;
  font-size: var(--extrasmallfont);
  margin-top: var(--spacing-xxs, 5px);
}

/* Phone Input Styles - Matching Auth.jsx styling */
.signup__page .signup__phone-container {
  margin-bottom: var(--spacing-md, 20px);
}

.signup__page .signup__phone-wrapper {
  display: flex;
  width: 100%;
}

.signup__page .signup__country-code-select {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 12px 10px;
  border: 1px solid var(--light-gray);
  border-right: none;
  border-radius: var(--border-radius-medium) 0 0 var(--border-radius-medium);
  background-color: var(--white);
  color: var(--text-color);
  font-size: var(--basefont);
  cursor: pointer;
}

.signup__page .signup__country-code-select:focus {
  border-color: var(--btn-color);
  outline: none;
}

.signup__page .signup__phone-input {
  flex: 1;
  border-radius: 0 var(--border-radius-medium) var(--border-radius-medium) 0 !important;
  padding: 12px 16px;
  border: 1px solid var(--light-gray);
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: var(--white);
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.signup__page .signup__phone-input:focus {
  border-color: var(--btn-color);
  
  outline: none;
}

.signup__page .signup__phone-input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

/* Terms Checkbox */
.signup__page .signup__terms {
  display: flex;
  align-items: flex-start;
  margin-bottom: var(--spacing-md, 20px);
  position: relative;
}

.signup__page .signup__checkbox {
  margin-right: var(--spacing-xs, 10px);
  margin-top: 2px;
  width: 16px;
  height: 16px;
  accent-color: var(--btn-color);
}

.signup__page .signup__checkbox--error {
  border: 2px solid #ff3b30;
  border-radius: 3px;
}

.signup__page .signup__terms-label {
  font-size: var(--smallfont);
  color: var(--secondary-color);
  position: relative;
}

.signup__page .signup__required-asterisk {
  color: #ff3b30;
  font-weight: bold;
  margin-right: 4px;
}

.signup__page .signup__terms-link {
  color: var(--btn-color);
  text-decoration: none;
}

.signup__page .signup__terms-link:hover {
  text-decoration: underline;
}

/* Button and Login Link */
.signup__page .signup__button {
  cursor: pointer;
  transition: all 0.3s ease;


     padding: 14px 28px;

     background: linear-gradient(to bottom, var(--btn-color), #ec1d3b);
  color: var(--white);

  border-radius: var(--border-radius-medium);
  font-weight: 600;
  font-size: var(--basefont);
  text-align: center;
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
  border: none !important;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  transition: all 0.3s ease;
  cursor: pointer;
  text-decoration: none;
}

.signup__page .signup__login-link {
  text-align: center;
  font-size: var(--smallfont);
  color: var(--secondary-color);
}

.signup__page .signup__link {
  color: var(--btn-color);
  text-decoration: none;
  font-weight: 500;
}

.signup__page .signup__link:hover {
  text-decoration: underline;
}

.signup__page .signup__divider {
  display: flex;
  align-items: center;
  margin: 20px 0;
  text-align: center;
}

.signup__page .signup__divider::before,
.signup__page .signup__divider::after {
  content: "";
  flex: 1;
  height: 1px;
  background: var(--light-gray);
}

.signup__page .signup__divider span {
  padding: 0 16px;
  color: var(--dark-gray);
  font-size: var(--smallfont);
  font-weight: 500;
}

/* Responsive styles */
@media (max-width: 768px) {
  .signup__page .signup__container {
    padding: var(--spacing-md, 20px);
  }

  .signup__page .signup__form-row {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .signup__page .signup__country-code-select {
    padding: 10px 8px;
    font-size: calc(var(--basefont) - 1px);
  }
}

@media (max-width: 480px) {
  .signup__page {
    padding: var(--spacing-sm, 15px) var(--spacing-xs, 10px);
  }

  .signup__page .signup__container {
    padding: var(--spacing-sm, 15px);
  }

  .signup__page .signup__title {
    font-size: var(--heading5);
  }

  .signup__page .signup__options {
    flex-direction: column;
    width: 100%;
  }

  .signup__page .signup__option {
    width: 100%;
    align-items: center;
    gap: var(--spacing-sm, 15px);
  }

  .signup__page .signup__option-content {
    flex-direction: row;
    align-items: center;
    gap: var(--spacing-xs, 10px);
  }

  .signup__page .signup__option-icon {
    margin-bottom: 0;
  }

  .signup__page .signup__country-code-select {
    padding: 8px 6px;
  }
}
