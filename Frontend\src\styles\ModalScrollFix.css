/* Universal Modal Scroll Containment Fix */

/* Apply scroll prevention to all modal overlays */
.modal-overlay,
.bid-modal-overlay,
.offer-modal-overlay,
.preview-modal-overlay,
.request-modal-overlay,
.confirmation-modal-overlay,
.role-modal-overlay,
.seller-offer-management .offer-modal-overlay,
.SellerOffers .modal-overlay,
.OfferDetails__modal-overlay {
  /* Prevent scroll propagation to background */
  overscroll-behavior: contain;
  /* Ensure overlay captures all scroll events */
  touch-action: none;
}

/* Allow touch actions within modal content areas */
/* .modal-content, */
.bid-modal,
.offer-modal,
.preview-modal,
.request-modal,
.confirmation-modal,
.role-modal,
.auction-bid-modal,
.offer-acceptance-modal,
.seller-offer-management .offer-review-modal,
.SellerOffers .response-modal,
.OfferDetails__response-modal {
  /* Enable proper scroll containment */
  overscroll-behavior: contain;
  /* Allow touch actions within modal content */
  touch-action: auto;
  /* Enable smooth scrolling */
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Enhanced scroll containment for nested scrollable areas */

.bid-modal *,
.offer-modal *,
.preview-modal *,
.request-modal *,
.confirmation-modal *,
.role-modal *,
.auction-bid-modal *,
.offer-acceptance-modal *,
.seller-offer-management .offer-review-modal *,
.SellerOffers .response-modal *,
.OfferDetails__response-modal * {
  /* Ensure all child elements respect scroll containment */
  overscroll-behavior: contain;
}

/* Body class for when any modal is open */
body.modal-open {
  overflow: hidden !important;
  position: fixed !important;
  width: 100% !important;
  top: 0 !important;
}

/* Prevent background scroll on mobile devices */
@media (max-width: 768px) {
  .modal-overlay,
  .bid-modal-overlay,
  .offer-modal-overlay,
  .preview-modal-overlay,
  .request-modal-overlay,
  .confirmation-modal-overlay,
  .role-modal-overlay,
  .seller-offer-management .offer-modal-overlay,
  .SellerOffers .modal-overlay,
  .OfferDetails__modal-overlay {
    /* More aggressive scroll prevention on mobile */
    position: fixed;
    overflow: hidden;
    -webkit-overflow-scrolling: touch;
  }
  
  /* Ensure modal content can still scroll on mobile */

  .bid-modal,
  .offer-modal,
  .preview-modal,
  .request-modal,
  .confirmation-modal,
  .role-modal,
  .auction-bid-modal,
  .offer-acceptance-modal,
  .seller-offer-management .offer-review-modal,
  .SellerOffers .response-modal,
  .OfferDetails__response-modal {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
    max-height: 90vh;
  }
}

/* iOS Safari specific fixes */
@supports (-webkit-touch-callout: none) {
  .modal-overlay,
  .bid-modal-overlay,
  .offer-modal-overlay,
  .preview-modal-overlay,
  .request-modal-overlay,
  .confirmation-modal-overlay,
  .role-modal-overlay,
  .seller-offer-management .offer-modal-overlay,
  .SellerOffers .modal-overlay,
  .OfferDetails__modal-overlay {
    /* iOS Safari scroll fix */
    position: fixed;
    -webkit-overflow-scrolling: touch;
    overflow: hidden;
  }
}

/* Android Chrome specific fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .modal-overlay,
  .bid-modal-overlay,
  .offer-modal-overlay,
  .preview-modal-overlay,
  .request-modal-overlay,
  .confirmation-modal-overlay,
  .role-modal-overlay,
  .seller-offer-management .offer-modal-overlay,
  .SellerOffers .modal-overlay,
  .OfferDetails__modal-overlay {
    /* Android Chrome scroll fix */
    touch-action: none;
    overscroll-behavior: none;
  }
}
