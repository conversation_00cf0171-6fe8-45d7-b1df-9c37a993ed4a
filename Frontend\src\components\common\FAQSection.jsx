import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MdAdd, Md<PERSON><PERSON><PERSON>, MdSearch } from "react-icons/md";
import { faqService } from "../../services/faqService";
import "../../styles/FAQSection.css";

const FAQSection = () => {
  const [faqs, setFaqs] = useState([]);
  const [filteredFaqs, setFilteredFaqs] = useState([]);
  const [activeFAQ, setActiveFAQ] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [categories, setCategories] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState("all");

  // Fetch FAQs and categories on component mount
  useEffect(() => {
    const fetchFAQData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch FAQs and categories in parallel
        const [faqResponse, categoriesResponse] = await Promise.all([
          faqService.getActiveFAQs(),
          faqService.getFAQCategories()
        ]);

        setFaqs(faqResponse.data || []);
        setFilteredFaqs(faqResponse.data || []);
        setCategories(['all', ...(categoriesResponse.data || [])]);
      } catch (err) {
        console.error('Error fetching FAQ data:', err);
        setError('Failed to load FAQs. Please try again later.');
      } finally {
        setLoading(false);
      }
    };

    fetchFAQData();
  }, []);

  // Filter FAQs based on search term and category
  useEffect(() => {
    let filtered = faqs;

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(faq => faq.category === selectedCategory);
    }

    // Filter by search term
    if (searchTerm.trim()) {
      const searchLower = searchTerm.toLowerCase();
      filtered = filtered.filter(faq =>
        faq.question.toLowerCase().includes(searchLower) ||
        faq.answer.toLowerCase().includes(searchLower)
      );
    }

    setFilteredFaqs(filtered);
    setActiveFAQ(null); // Reset active FAQ when filtering
  }, [faqs, searchTerm, selectedCategory]);

  const toggleFAQ = (id) => {
    setActiveFAQ(activeFAQ === id ? null : id);
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleCategoryChange = (category) => {
    setSelectedCategory(category);
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const answerVariants = {
    hidden: { opacity: 0, height: 0 },
    visible: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    },
    exit: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut"
      }
    }
  };

  if (loading) {
    return (
      <section className="faq-section p-section">
        <div className="faq-container max-container">
          <div className="faq-loading">
            <div className="loading-spinner"></div>
            <p>Loading FAQs...</p>
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="faq-section p-section">
        <div className="faq-container max-container">
          <div className="faq-error">
            <p>{error}</p>
          </div>
        </div>
      </section>
    );
  }

  return (
    <motion.section
      className="faq-section p-section"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, amount: 0.2 }}
      variants={containerVariants}
    >
      <div className="faq-container max-container">
        <motion.div className="faq-header" variants={itemVariants}>
          <h2 className="faq-title">Frequently Asked Questions</h2>
          <p className="faq-subtitle">
            Find answers to common questions about XOSportsHub
          </p>
        </motion.div>

        {/* Search and Filter Controls */}
        <motion.div className="faq-controls" variants={itemVariants}>
          <div className="faq-search">
            <MdSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search FAQs..."
              value={searchTerm}
              onChange={handleSearchChange}
              className="search-input"
            />
          </div>

          {categories.length > 2 && (
            <div className="faq-categories">
              {categories.map((category) => (
                <button
                  key={category}
                  className={`category-btn ${selectedCategory === category ? 'active' : ''}`}
                  onClick={() => handleCategoryChange(category)}
                >
                  {category === 'all' ? 'All' : category.charAt(0).toUpperCase() + category.slice(1)}
                </button>
              ))}
            </div>
          )}
        </motion.div>

        {/* FAQ List */}
        <motion.div className="faq-list" variants={itemVariants}>
          {filteredFaqs.length === 0 ? (
            <div className="no-faqs">
              <p>No FAQs found matching your criteria.</p>
            </div>
          ) : (
            filteredFaqs.map((faq, index) => (
              <motion.div
                key={faq._id}
                className="faq-item"
                variants={itemVariants}
                initial="hidden"
                animate="visible"
                transition={{ delay: index * 0.1 }}
              >
                <button
                  className={`faq-question ${activeFAQ === faq._id ? "active" : ""}`}
                  onClick={() => toggleFAQ(faq._id)}
                  aria-expanded={activeFAQ === faq._id}
                  aria-controls={`faq-answer-${faq._id}`}
                >
                  <span className="faq-question-text">{faq.question}</span>
                  <span className="faq-icon">
                    {activeFAQ === faq._id ? <MdRemove /> : <MdAdd />}
                  </span>
                </button>
                
                <AnimatePresence>
                  {activeFAQ === faq._id && (
                    <motion.div
                      id={`faq-answer-${faq._id}`}
                      className="faq-answer"
                      variants={answerVariants}
                      initial="hidden"
                      animate="visible"
                      exit="exit"
                    >
                      <div className="faq-answer-content">
                        {faq.answer}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </motion.div>
            ))
          )}
        </motion.div>
      </div>
    </motion.section>
  );
};

export default FAQSection;
