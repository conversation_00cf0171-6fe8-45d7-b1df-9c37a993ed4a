import React from 'react';
import PropTypes from 'prop-types';
import './AdminPagination.css';

const AdminPagination = ({
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onPageChange,
  isLoading = false,
  className = '',
  showInfo = true,
  showPageNumbers = true
}) => {
  // Calculate items being shown
  const startItem = totalItems > 0 ? (currentPage - 1) * itemsPerPage + 1 : 0;
  const endItem = Math.min(currentPage * itemsPerPage, totalItems);

  const renderPaginationItems = () => {
    const items = [];

    // Previous button
    items.push(
      <button
        key="prev"
        className="admin-pagination__btn admin-pagination__btn--prev"
        onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
        disabled={currentPage === 1 || isLoading}
        title="Previous page"
      >
        Previous
      </button>
    );

    if (showPageNumbers) {
      // Page numbers with smart ellipsis
      const maxVisiblePages = 5;
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      // Adjust start if we're near the end
      if (endPage - startPage < maxVisiblePages - 1) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      // First page + ellipsis
      if (startPage > 1) {
        items.push(
          <button
            key={1}
            className="admin-pagination__btn admin-pagination__btn--page"
            onClick={() => onPageChange(1)}
            disabled={isLoading}
          >
            1
          </button>
        );
        if (startPage > 2) {
          items.push(
            <span key="ellipsis-start" className="admin-pagination__ellipsis">
              ...
            </span>
          );
        }
      }

      // Visible page numbers
      for (let i = startPage; i <= endPage; i++) {
        items.push(
          <button
            key={i}
            className={`admin-pagination__btn admin-pagination__btn--page ${
              currentPage === i ? 'admin-pagination__btn--active' : ''
            }`}
            onClick={() => onPageChange(i)}
            disabled={isLoading}
          >
            {i}
          </button>
        );
      }

      // Last page + ellipsis
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          items.push(
            <span key="ellipsis-end" className="admin-pagination__ellipsis">
              ...
            </span>
          );
        }
        items.push(
          <button
            key={totalPages}
            className="admin-pagination__btn admin-pagination__btn--page"
            onClick={() => onPageChange(totalPages)}
            disabled={isLoading}
          >
            {totalPages}
          </button>
        );
      }
    } else {
      // Simple current page display
      items.push(
        <span key="current" className="admin-pagination__current">
          {currentPage}
        </span>
      );
    }

    // Next button
    items.push(
      <button
        key="next"
        className="admin-pagination__btn admin-pagination__btn--next"
        onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages || isLoading}
        title="Next page"
      >
        Next
      </button>
    );

    return items;
  };

  // Don't render if no pagination needed
  if (totalPages <= 1) return null;

  return (
    <div className={`admin-pagination ${className}`.trim()}>
      {showInfo && (
        <div className="admin-pagination__info">
          Showing {startItem}-{endItem} of {totalItems} items
        </div>
      )}
      <div className="admin-pagination__controls">
        {renderPaginationItems()}
      </div>
    </div>
  );
};

AdminPagination.propTypes = {
  currentPage: PropTypes.number.isRequired,
  totalPages: PropTypes.number.isRequired,
  totalItems: PropTypes.number.isRequired,
  itemsPerPage: PropTypes.number.isRequired,
  onPageChange: PropTypes.func.isRequired,
  isLoading: PropTypes.bool,
  className: PropTypes.string,
  showInfo: PropTypes.bool,
  showPageNumbers: PropTypes.bool
};

export default AdminPagination;
