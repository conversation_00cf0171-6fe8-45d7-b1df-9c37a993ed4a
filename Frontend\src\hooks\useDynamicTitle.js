import { useEffect } from 'react';
import { usePublicSettings } from './useSettings';

/**
 * Custom hook to dynamically update the document title based on settings
 */
export const useDynamicTitle = (pageTitle = '') => {
  const { siteName } = usePublicSettings();

  useEffect(() => {
    const updateTitle = () => {
      const baseTitle = siteName || 'XO Sports Hub';
      const fullTitle = pageTitle ? `${pageTitle} - ${baseTitle}` : baseTitle;
      document.title = fullTitle;
    };

    // Update title when settings or pageTitle change
    updateTitle();
  }, [siteName, pageTitle]);

  return null; // This hook doesn't return anything, just manages side effects
};

export default useDynamicTitle;
