/* Maintenance Page Styles */
.maintenance-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  position: relative;
  overflow: hidden;
  font-family: 'Poppins', sans-serif;
}

.maintenance-page__container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 900px;
  padding: 2rem;
  text-align: center;
}

.maintenance-page__content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-large);
  padding: 3rem 2rem;
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.maintenance-page__header {
  margin-bottom: 2.5rem;
}

.maintenance-page__logo {
  max-height: 80px;
  width: auto;
  margin-bottom: 1.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.maintenance-page__icon-group {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.maintenance-page__icon {
  font-size: 2.5rem;
  animation: maintenance-pulse 2s ease-in-out infinite;
}

.maintenance-page__icon--primary {
  color: #ff6b6b;
  animation-delay: 0s;
}

.maintenance-page__icon--secondary {
  color: #ee5a24;
  animation-delay: 0.3s;
}

.maintenance-page__icon--tertiary {
  color: #ff9ff3;
  animation-delay: 0.6s;
}

.maintenance-page__title {
  font-size: var(--heading2);
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
}

.maintenance-page__maintenance {
  margin-bottom: 2.5rem;
}

.maintenance-page__status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(255, 107, 107, 0.1);
  border-radius: var(--border-radius);
  border: 1px solid rgba(255, 107, 107, 0.2);
}

.maintenance-page__status-icon {
  color: #ff6b6b;
  font-size: 1.25rem;
}

.maintenance-page__status-text {
  font-weight: 600;
  color: #d63031;
  margin: 0;
  font-size: var(--body-text);
}

.maintenance-page__message {
  font-size: var(--body-text-large);
  color: var(--text-color);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.maintenance-page__submessage {
  font-size: var(--body-text);
  color: var(--text-color-light);
  margin-bottom: 2rem;
  line-height: 1.5;
}



.maintenance-page__features {
  text-align: left;
  max-width: 400px;
  margin: 0 auto 2rem;
}

.maintenance-page__features-title {
  font-size: var(--body-text-large);
  font-weight: 600;
  color: var(--text-color);
  margin-bottom: 1rem;
  text-align: center;
}

.maintenance-page__features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.maintenance-page__features-list li {
  padding: 0.5rem 0;
  color: var(--text-color-light);
  position: relative;
  padding-left: 1.5rem;
}

.maintenance-page__features-list li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #28a745;
  font-weight: bold;
}



/* Admin Login Button */
.maintenance-page__admin-login {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.admin-login-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--small-text);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10;
  outline: none;
}

.admin-login-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-login-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.admin-login-btn:active {
  transform: translateY(0);
}

.admin-login-btn svg {
  font-size: 1rem;
}

/* Background Animation */
.maintenance-page__background {
  position: relative;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
  overflow: hidden;
}



.maintenance-page__floating-icons {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-icon {
  position: absolute;
  color: rgba(255, 255, 255, 0.1);
  font-size: 2rem;
  animation: float 6s ease-in-out infinite;
}

.floating-icon--1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.floating-icon--2 {
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.floating-icon--3 {
  bottom: 30%;
  left: 20%;
  animation-delay: 2s;
}

.floating-icon--4 {
  top: 40%;
  right: 30%;
  animation-delay: 3s;
}

.floating-icon--5 {
  bottom: 20%;
  right: 10%;
  animation-delay: 4s;
}

/* Animations */
@keyframes maintenance-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes background-move {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-20px) rotate(5deg);
  }
  66% {
    transform: translateY(10px) rotate(-5deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .maintenance-page__container {
    padding: 1rem;
  }

  .maintenance-page__content {
    padding: 2rem 1.5rem;
  }

  .maintenance-page__title {
    font-size: var(--heading3);
  }

  .maintenance-page__icon-group {
    gap: 0.75rem;
  }

  .maintenance-page__icon {
    font-size: 2rem;
  }

  .maintenance-page__message {
    font-size: var(--body-text);
  }

  .maintenance-page__eta {
    flex-direction: column;
    text-align: center;
  }

  .maintenance-page__eta-content {
    align-items: center;
  }

  .maintenance-page__features {
    max-width: 100%;
  }

  .maintenance-page__social {
    gap: 0.75rem;
  }

  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .admin-login-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.8rem;
  }

  .floating-icon {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  .maintenance-page__title {
    font-size: var(--heading4);
  }

  .maintenance-page__icon {
    font-size: 1.75rem;
  }

  .maintenance-page__status {
    flex-direction: column;
    gap: 0.5rem;
  }

  .maintenance-page__eta {
    padding: 0.75rem;
  }

  .social-link {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }

  .floating-icon {
    font-size: 1.25rem;
  }
}
