import React, { useState } from "react";
import { useNavi<PERSON>, Link } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import "../../styles/Auth.css";
import { login, reset, googleSignIn } from "../../redux/slices/authSlice";

import toast from "../../utils/toast";
import GoogleSignInButton from "../../components/common/GoogleSignInButton";
import { MdMailOutline } from "react-icons/md";
import firebaseService from "../../services/firebaseService";
import { getSellerRedirectPath } from "../../utils/sellerUtils";

const Auth = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { isLoading } = useSelector(
    (state) => state.auth
  );

  const [formData, setFormData] = useState({
    email: "",
  });

  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox" ? checked : value,
    });

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: null,
      });
    }
  };



  const validateForm = () => {
    const newErrors = {};

    // Validate email
    if (!formData.email) {
      newErrors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    }

    return newErrors;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    const validationErrors = validateForm();
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    // Reset any previous errors
    dispatch(reset());

    try {
      // Dispatch login action with email
      const result = await dispatch(login({ email: formData.email })).unwrap();

      // Show success message and immediately redirect for better UX
      toast.otp.success("OTP sent successfully!");

      // Navigate to OTP verification page with user ID and email
      navigate("/otp-verification", {
        state: {
          userId: result.userId,
          email: formData.email,
          cooldownSeconds: result.cooldownSeconds || 60,
          isLogin: true,
          developmentOtp: result.developmentOtp, // Pass development OTP if available
        },
      });
    } catch (error) {
      //console.error("Login error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Login failed. Please try again.";

      if (typeof error === 'string') {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Handle different types of errors
      if (errorMessage.includes("wait") && errorMessage.includes("seconds")) {
        // Cooldown error
        const seconds = errorMessage.match(/\d+/)?.[0] || 60;
        toast.otp.cooldown(parseInt(seconds));
      } else if (errorMessage.includes("User not found")) {
        toast.error("No account found with this email. Please sign up first.");
      } else {
        // Show the actual backend error message
        toast.error(errorMessage);
      }
    }
  };

  // Handle Google Sign-In
  const handleGoogleSignIn = async () => {
    try {
      dispatch(reset());

      // Check if Firebase is initialized
      if (!firebaseService.isInitialized()) {
        toast.error(
          "Firebase is not initialized. Please check your configuration."
        );
        return;
      }

      // Sign in with Google using Firebase
      const result = await firebaseService.signInWithGoogle();

      // Try to sign in with existing account
      try {
        const response = await dispatch(googleSignIn(result.idToken)).unwrap();

        // Success - user exists and is logged in
        toast.auth.loginSuccess();

        // Navigate based on user role and onboarding status
        if (response.user.role === "buyer") {
          navigate("/content");
        } else if (response.user.role === "seller") {
          // For sellers, check onboarding status and redirect accordingly
          const redirectPath = getSellerRedirectPath(response.user);
          navigate(redirectPath);
        } else if (response.user.role === "admin") {
          navigate("/admin/dashboard");
        } else {
          navigate("/");
        }
      } catch (signInError) {
        // User doesn't exist - automatically create account with default "buyer" role
        const errorMessage =
          typeof signInError === "string"
            ? signInError
            : signInError?.message || "";
        if (
          errorMessage.includes("not found") ||
          errorMessage.includes("does not exist")
        ) {
          // Automatically create user as buyer (default role)
          try {
            const { googleSignUp } = await import("../../redux/slices/authSlice");

            await dispatch(
              googleSignUp({ idToken: result.idToken, role: "buyer" })
            ).unwrap();

            // Success - user created and logged in as buyer
            toast.auth.registrationSuccess();

            // Navigate to buyer dashboard
            navigate("/content");
          } catch (signUpError) {
            console.error("Google sign-up error:", signUpError);

            // Extract the error message from the formatted error object
            let errorMessage = "Failed to complete registration. Please try again.";

            if (typeof signUpError === "string") {
              errorMessage = signUpError;
            } else if (signUpError?.message) {
              errorMessage = signUpError.message;
            }

            // Show the actual backend error message
            toast.error(errorMessage);
          }
        } else {
          throw signInError;
        }
      }
    } catch (error) {
      console.error("Google sign-in error:", error);

      // Extract the error message from the formatted error object
      let errorMessage = "Failed to sign in with Google. Please try again.";

      if (typeof error === "string") {
        errorMessage = error;
      } else if (error?.message) {
        errorMessage = error.message;
      }

      // Show the actual backend error message
      toast.error(errorMessage);
    }
  };



  return (
    <div className="auth-page auth-container">
      <div className="auth-form-container">
        <h1 className="auth-title">Login in to your account</h1>

        <form onSubmit={handleSubmit} className="auth-form">
          <div className="auth-form-input form-input-container">
            <div className="email-input-wrapper">
              <div className="email-input-icon">
                <MdMailOutline />
              </div>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                placeholder="Enter your email address"
                className={`form-input ${errors.email ? "error" : ""}`}
                required
              />
            </div>
            {errors.email && <p className="phone-error-message">{errors.email}</p>}
          </div>

          <button type="submit" className="signin-button" disabled={isLoading}>
            {isLoading ? "Sending OTP..." : "Send Code Now"}
          </button>

          <div className="auth-divider">
            <span>or</span>
          </div>

          <GoogleSignInButton
            onClick={handleGoogleSignIn}
            isLoading={isLoading}
            text="Sign in with Google"
            variant="secondary"
          />

          <p className="signup-link mt-10">
            Don't have an account? <Link to="/signup">Sign Up</Link>
          </p>
        </form>
      </div>
    </div>
  );
};

export default Auth;
