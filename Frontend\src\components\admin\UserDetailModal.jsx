import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  selectCurrentUserDetail,
  selectLoading,
  hideUserDetailModal,
  setUserDetailLoading,
  addActivity,
} from "../../redux/slices/adminDashboardSlice";
import {
  updateUser,
  deleteUser,
  fetchUserById,
} from "../../redux/slices/adminDashboardThunks";
import { showSuccess, showError } from "../../utils/toast";
import "../../styles/UserDetailModal.css";

// Icons
import {
  FaTimes,
  FaUser,
  FaEnvelope,
  FaPhone,
  FaCalendarAlt,
  FaDollarSign,
  FaShoppingCart,
  FaEdit,
  FaTrash,
  FaCheckCircle,
  FaTimesCircle,
  FaSave,
  FaSpinner,
  FaExclamationTriangle,
} from "react-icons/fa";
import { MdVerified, MdBlock } from "react-icons/md";
import { IMAGE_BASE_URL } from "../../utils/constants";

const UserDetailModal = () => {
  const dispatch = useDispatch();
  const currentUser = useSelector(selectCurrentUserDetail);
  const loading = useSelector(selectLoading);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Helper function to convert numeric status to string
  const getStatusString = (status) => {
    switch (status) {
      case 1:
        return "active";
      case 0:
        return "inactive";
      case -1:
        return "deleted";
      default:
        return "active";
    }
  };

  // Helper function to convert string status to numeric
  const getStatusNumber = (status) => {
    switch (status) {
      case "active":
        return 1;
      case "inactive":
        return 0;
      case "deleted":
        return -1;
      default:
        return 1;
    }
  };

  if (!currentUser) {
    return null;
  }

  const handleClose = () => {
    dispatch(hideUserDetailModal());
    setShowDeleteConfirm(false);
    setIsSubmitting(false);
  };

  const handleToggleStatus = async () => {
    const currentStatusNum = currentUser.status;
    const newStatusNum = currentStatusNum === 1 ? 0 : 1; // Toggle between active (1) and inactive (0)
    const actionText = newStatusNum === 1 ? "activate" : "deactivate";

    if (!window.confirm(`Are you sure you want to ${actionText} this user?`)) {
      return;
    }

    try {
      setIsSubmitting(true);
      dispatch(setUserDetailLoading(true));

      // Dispatch the async thunk
      await dispatch(
        updateUser({
          id: currentUser.id || currentUser._id,
          userData: { status: newStatusNum },
        })
      ).unwrap();

      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_status_change",
          description: `User ${getStatusString(newStatusNum)}: ${currentUser.firstName
            } ${currentUser.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      // Refresh user data
      if (currentUser.id || currentUser._id) {
        dispatch(fetchUserById(currentUser.id || currentUser._id));
      }

      showSuccess(`User has been ${actionText}d successfully!`);
    } catch (error) {
      console.error("Failed to update user status:", error);
      setFormErrors({
        general:
          error.message || "Failed to update user status. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
      dispatch(setUserDetailLoading(false));
    }
  };

  const handleDelete = async () => {
    try {
      setIsSubmitting(true);
      dispatch(setUserDetailLoading(true));

      // Dispatch the async thunk
      await dispatch(deleteUser(currentUser.id || currentUser._id)).unwrap();

      dispatch(
        addActivity({
          id: Date.now(),
          type: "user_deletion",
          description: `User deleted: ${currentUser.firstName} ${currentUser.lastName}`,
          timestamp: new Date().toISOString(),
          user: "Admin",
        })
      );

      handleClose();
      showSuccess(
        `User "${currentUser.firstName} ${currentUser.lastName}" has been deleted successfully!`
      );
    } catch (error) {
      console.error("Failed to delete user:", error);
      setFormErrors({
        general: error.message || "Failed to delete user. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
      dispatch(setUserDetailLoading(false));
    }
  };

  const confirmDelete = () => {
    setShowDeleteConfirm(true);
  };

  const cancelDelete = () => {
    setShowDeleteConfirm(false);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 1:
        return <FaCheckCircle className="status-icon active" title="Active" />;
      case 0:
        return (
          <FaTimesCircle className="status-icon inactive" title="Inactive" />
        );
      case -1:
        return <FaTrash className="status-icon deleted" title="Deleted" />;
      default:
        return (
          <FaTimesCircle className="status-icon inactive" title="Unknown" />
        );
    }
  };

  const getRoleBadge = (role) => {
    const badges = {
      buyer: { color: "#3b82f6", label: "Buyer" },
      seller: { color: "#10b981", label: "Seller" },
      admin: { color: "#f59e0b", label: "Admin" },
    };
    const badge = badges[role] || badges.buyer;

    return (
      <span
        className="role-badge"
        style={{ backgroundColor: `${badge.color}20`, color: badge.color }}
      >
        {badge.label}
      </span>
    );
  };

  return (
    <div className="UserDetailModal">
      <div className="UserDetailModal__overlay" onClick={handleClose} />
      <div className="UserDetailModal__container">
        {/* Header */}
        <div className="UserDetailModal__header">
          <div className="header-info">
            <div className="user-avatar">
              {currentUser?.profileImage ? (
                <img
                  src={IMAGE_BASE_URL + currentUser.profileImage}
                  alt={currentUser?.firstName || "User"}
                />
              ) : (
                <FaUser />
              )}
            </div>
            <div className="user-basic-info">
              <h2>
                {currentUser?.firstName || "Unknown"}{" "}
                {currentUser?.lastName || "User"}
              </h2>
              <div className="user-badges">
                {getRoleBadge(currentUser?.role === 'admin' ? currentUser?.role : currentUser?.activeRole)}
                {getStatusIcon(currentUser?.status)}
                {currentUser?.verificationStatus === "verified" && (
                  <MdVerified
                    className="verification-icon verified"
                    title="Verified User"
                  />
                )}
              </div>
            </div>
          </div>
          <button className="close-btn" onClick={handleClose}>
            <FaTimes />
          </button>
        </div>

        {/* Content */}
        <div className="UserDetailModal__content">
          {/* User Information */}
          <div className="info-section">
            <div className="section-header">
              <h3>User Information</h3>
            </div>

            <div className="info-grid">
              <div className="info-item">
                <FaEnvelope className="info-icon" />
                <div>
                  <span className="info-label">Email</span>
                  <span className="info-value">{currentUser.email}</span>
                </div>
              </div>
              <div className="info-item">
                <FaPhone className="info-icon" />
                <div>
                  <span className="info-label">Phone</span>
                  <span className="info-value">{currentUser.mobile}</span>
                </div>
              </div>
              <div className="info-item">
                <FaCalendarAlt className="info-icon" />
                <div>
                  <span className="info-label">Date Joined</span>
                  <span className="info-value">
                    {formatDate(currentUser.createdAt)}
                  </span>
                </div>
              </div>
              <div className="info-item">
                <FaCalendarAlt className="info-icon" />
                <div>
                  <span className="info-label">Last Login</span>
                  <span className="info-value">
                    {formatDate(currentUser.lastLogin)}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Statistics */}
          {(currentUser.role === 'admin' ? currentUser.role : currentUser.activeRole) === "buyer" && (
            <div className="stats-section">
              <h3 className="pb-5">Purchase Statistics</h3>
              <div className="stats-grid">
                <div className="stat-card">
                  <FaShoppingCart className="stat-icon" />
                  <div className="stat-content">
                    <span className="stat-number">
                      {currentUser.totalPurchases}
                    </span>
                    <span className="stat-label">Total Purchases</span>
                  </div>
                </div>
                <div className="stat-card">
                  <FaDollarSign className="stat-icon" />
                  <div className="stat-content">
                    <span className="stat-number">
                      {formatCurrency(currentUser.totalSpent)}
                    </span>
                    <span className="stat-label">Total Spent</span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Activity History */}
          <div className="activity-section">
            <h3>Recent Activity</h3>
            <div className="activity-list">
              {currentUser.activityHistory?.map((activity, index) => (
                <div key={index} className="activity-item">
                  <div className="activity-content">
                    <span className="activity-description">
                      {activity.action}
                    </span>
                    <span className="activity-date">
                      {formatDate(activity.date)}
                    </span>
                  </div>
                  {activity.amount && (
                    <span className="activity-amount">
                      {formatCurrency(activity.amount)}
                    </span>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="actions-section">
            <div className="action-buttons">
              <button
                className={`btn ${currentUser?.status === 1 ? "btn-warning" : "btn-success"
                  }`}
                onClick={handleToggleStatus}
                disabled={
                  isSubmitting ||
                  loading?.userDetail ||
                  currentUser?.status === -1
                }
              >
                {isSubmitting ? (
                  <FaSpinner className="spinner" />
                ) : currentUser?.status === 1 ? (
                  <>
                    <MdBlock />
                    Deactivate User
                  </>
                ) : (
                  <>
                    <FaCheckCircle />
                    Activate User
                  </>
                )}
              </button>
              <button
                className="btn btn-danger"
                onClick={confirmDelete}
                disabled={isSubmitting || loading?.userDetail}
              >
                <FaTrash />
                Delete User
              </button>
            </div>
          </div>

          {/* Delete Confirmation Modal */}
          {showDeleteConfirm && (
            <div className="delete-confirm-overlay">
              <div className="delete-confirm-modal">
                <div className="delete-confirm-header">
                  <FaExclamationTriangle className="warning-icon" />
                  <h3>Confirm Deletion</h3>
                </div>
                <div className="delete-confirm-content">
                  <p>
                    Are you sure you want to delete user{" "}
                    <strong>
                      "{currentUser.firstName} {currentUser.lastName}"
                    </strong>
                    ?
                  </p>
                  <p className="warning-text">
                    This action cannot be undone. All user data will be
                    permanently removed.
                  </p>
                </div>
                <div className="delete-confirm-actions">
                  <button
                    className="btn btn-danger"
                    onClick={() => {
                      setShowDeleteConfirm(false);
                      handleDelete();
                    }}
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <FaSpinner className="spinner" />
                        Deleting...
                      </>
                    ) : (
                      <>
                        <FaTrash />
                        Yes, Delete User
                      </>
                    )}
                  </button>
                  <button
                    className="btn btn-outline"
                    onClick={cancelDelete}
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default UserDetailModal;
