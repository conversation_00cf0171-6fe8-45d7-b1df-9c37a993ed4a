import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import contactService from "../../services/contactService";
import { formatError } from "../../utils/errorHandler";

// Submit contact form
export const submitContactForm = createAsyncThunk(
  "contact/submitContactForm",
  async (contactData, thunkAPI) => {
    try {
      return await contactService.submitContactForm(contactData);
    } catch (error) {
      return thunkAPI.rejectWithValue(formatError(error));
    }
  }
);

// Initial state for the contact form
const initialState = {
  // Form data
  formData: {
    firstName: "",
    lastName: "",
    email: "",
    mobile: "",
    message: "",
  },

  // Form validation errors
  errors: {},

  // Form submission state
  isSubmitting: false,
  isSubmitted: false,
  isSuccess: false,
  isError: false,
  error: null,

  // Contact information
  contactInfo: {
    email: "<EMAIL>",
    socialLinks: {
      facebook: "#",
      instagram: "#",
      twitter: "#",
    },
  },
};

const contactSlice = createSlice({
  name: "contact",
  initialState,
  reducers: {
    // Update form field
    updateFormField: (state, action) => {
      const { field, value } = action.payload;
      state.formData[field] = value;

      // Clear error for this field when user starts typing
      if (state.errors[field]) {
        delete state.errors[field];
      }
    },

    // Set form errors
    setFormErrors: (state, action) => {
      state.errors = action.payload;
    },

    // Clear form errors
    clearFormErrors: (state) => {
      state.errors = {};
    },

    // Reset form
    resetForm: (state) => {
      state.formData = {
        firstName: "",
        lastName: "",
        email: "",
        mobile: "",
        message: "",
      };
      state.errors = {};
      state.isSubmitted = false;
      state.isSuccess = false;
      state.isError = false;
      state.error = null;
    },

    // Reset submission state
    resetSubmissionState: (state) => {
      state.isSubmitted = false;
      state.isSuccess = false;
      state.isError = false;
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Submit contact form
      .addCase(submitContactForm.pending, (state) => {
        state.isSubmitting = true;
        state.isError = false;
        state.error = null;
      })
      .addCase(submitContactForm.fulfilled, (state, action) => {
        state.isSubmitting = false;
        state.isSuccess = true;
        state.isSubmitted = true;
        // Reset form data on success
        state.formData = {
          firstName: "",
          lastName: "",
          email: "",
          mobile: "",
          message: "",
        };
        state.errors = {};
      })
      .addCase(submitContactForm.rejected, (state, action) => {
        state.isSubmitting = false;
        state.isError = true;
        state.error = action.payload;
      });
  },
});

export const {
  updateFormField,
  setFormErrors,
  clearFormErrors,
  resetForm,
  resetSubmissionState,
} = contactSlice.actions;

export default contactSlice.reducer;
