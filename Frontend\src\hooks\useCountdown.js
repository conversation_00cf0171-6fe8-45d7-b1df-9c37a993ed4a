import { useState, useEffect, useCallback } from 'react';

/**
 * Custom hook for countdown timer functionality
 * @param {string|Date} targetDate - The target date/time for countdown
 * @param {Object} options - Configuration options
 * @returns {Object} Countdown state and utilities
 */
export const useCountdown = (targetDate, options = {}) => {
  const {
    onComplete = null,
    interval = 1000,
    autoRefresh = true,
    refreshDelay = 2000
  } = options;

  const [timeLeft, setTimeLeft] = useState({
    days: 0,
    hours: 0,
    minutes: 0,
    seconds: 0,
    total: 0
  });

  const [isComplete, setIsComplete] = useState(false);
  const [isActive, setIsActive] = useState(false);

  // Calculate time remaining
  const calculateTimeLeft = useCallback(() => {
    if (!targetDate) {
      setIsActive(false);
      return;
    }

    const now = new Date().getTime();
    const target = new Date(targetDate).getTime();
    const difference = target - now;

    if (difference > 0) {
      const days = Math.floor(difference / (1000 * 60 * 60 * 24));
      const hours = Math.floor((difference % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
      const minutes = Math.floor((difference % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((difference % (1000 * 60)) / 1000);

      setTimeLeft({
        days,
        hours,
        minutes,
        seconds,
        total: difference
      });

      setIsActive(true);
      setIsComplete(false);
    } else {
      // Countdown completed
      setTimeLeft({
        days: 0,
        hours: 0,
        minutes: 0,
        seconds: 0,
        total: 0
      });

      setIsActive(false);
      setIsComplete(true);
    }
  }, [targetDate]);

  // Handle completion logic separately to avoid infinite loops
  useEffect(() => {
    if (isComplete && onComplete && typeof onComplete === 'function') {
      onComplete();

      // Auto refresh after delay if enabled
      if (autoRefresh) {
        const refreshTimer = setTimeout(() => {
          window.location.reload();
        }, refreshDelay);

        return () => clearTimeout(refreshTimer);
      }
    }
  }, [isComplete, onComplete, autoRefresh, refreshDelay]);

  // Set up interval
  useEffect(() => {
    if (!targetDate) return;

    // Calculate immediately
    calculateTimeLeft();

    // Set up interval
    const timer = setInterval(calculateTimeLeft, interval);

    return () => clearInterval(timer);
  }, [calculateTimeLeft, interval, targetDate]);

  // Format time units with leading zeros
  const formatTimeUnit = (value) => {
    return value.toString().padStart(2, '0');
  };

  // Get formatted time strings
  const formattedTime = {
    days: formatTimeUnit(timeLeft.days),
    hours: formatTimeUnit(timeLeft.hours),
    minutes: formatTimeUnit(timeLeft.minutes),
    seconds: formatTimeUnit(timeLeft.seconds)
  };

  // Get total time in different units
  const totalTime = {
    days: Math.floor(timeLeft.total / (1000 * 60 * 60 * 24)),
    hours: Math.floor(timeLeft.total / (1000 * 60 * 60)),
    minutes: Math.floor(timeLeft.total / (1000 * 60)),
    seconds: Math.floor(timeLeft.total / 1000),
    milliseconds: timeLeft.total
  };

  // Check if countdown is in final phase (less than 24 hours)
  const isFinalPhase = totalTime.hours < 24 && totalTime.hours >= 0;

  // Check if countdown is in critical phase (less than 1 hour)
  const isCriticalPhase = totalTime.minutes < 60 && totalTime.minutes >= 0;

  // Get progress percentage (0-100)
  const getProgress = (totalDuration) => {
    if (!totalDuration || totalTime.milliseconds <= 0) return 100;
    const elapsed = totalDuration - totalTime.milliseconds;
    return Math.min(100, Math.max(0, (elapsed / totalDuration) * 100));
  };

  // Reset countdown
  const reset = () => {
    setIsComplete(false);
    setIsActive(false);
    calculateTimeLeft();
  };

  return {
    // Time values
    timeLeft,
    formattedTime,
    totalTime,
    
    // State
    isActive,
    isComplete,
    isFinalPhase,
    isCriticalPhase,
    
    // Utilities
    getProgress,
    reset,
    formatTimeUnit
  };
};

/**
 * Hook specifically for launch countdown with site-specific logic
 * @param {string|Date} launchDate - Launch date/time
 * @returns {Object} Launch countdown state and utilities
 */
export const useLaunchCountdown = (launchDate) => {
  const countdown = useCountdown(launchDate, {
    onComplete: () => {
      console.log('🚀 Launch countdown completed!');
    },
    autoRefresh: true,
    refreshDelay: 2000
  });

  // Check if launch date is in the past (already launched)
  const isAlreadyLaunched = launchDate && new Date(launchDate) <= new Date();

  // Get launch status message
  const getLaunchStatusMessage = () => {
    if (!launchDate) return 'No launch date set';
    if (isAlreadyLaunched) return 'Site is live!';
    if (countdown.isCriticalPhase) return 'Launching very soon!';
    if (countdown.isFinalPhase) return 'Launching today!';
    return 'Coming soon!';
  };

  // Get time until launch in human readable format
  const getTimeUntilLaunch = () => {
    if (!launchDate || isAlreadyLaunched) return null;
    
    const { days, hours, minutes } = countdown.timeLeft;
    
    if (days > 0) {
      return `${days} day${days !== 1 ? 's' : ''} remaining`;
    } else if (hours > 0) {
      return `${hours} hour${hours !== 1 ? 's' : ''} remaining`;
    } else if (minutes > 0) {
      return `${minutes} minute${minutes !== 1 ? 's' : ''} remaining`;
    } else {
      return 'Launching now!';
    }
  };

  return {
    ...countdown,
    isAlreadyLaunched,
    getLaunchStatusMessage,
    getTimeUntilLaunch
  };
};
