/* Launch Countdown Page Styles */
.launch-countdown {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--second-primary-color) 100%);
  position: relative;
  overflow: hidden;
  font-family: 'Poppins', sans-serif;
}

.launch-countdown__container {
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 800px;
  padding: 2rem;
  text-align: center;
}

.launch-countdown__content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-large);
  padding: 3rem 2rem;
  box-shadow: var(--box-shadow);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.launch-countdown__header {
  margin-bottom: 2rem;
}

.launch-countdown__logo {
  max-height: 80px;
  width: auto;
  margin-bottom: 1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.launch-countdown__title {
  font-size: var(--heading2);
  font-weight: 700;
  color: var(--text-color);
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
}

.launch-countdown__icon {
  color: var(--primary-color);
  font-size: 1.2em;
}

.launch-countdown__message {
  font-size: var(--heading5);
  color: var(--text-color);
  margin: 1rem 0;
  font-weight: 500;
}

.launch-countdown__submessage {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin: 0.5rem 0 2rem;
}

/* Timer Styles */
.launch-countdown__timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  flex-wrap: wrap;
}

.timer-unit {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--white);
  border-radius: var(--border-radius);
  padding: 1.5rem 1rem;
  min-width: 100px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 2px solid var(--primary-light-color);
}

.timer-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
  margin-bottom: 0.5rem;
}

.timer-label {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.timer-separator {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
  margin: 0 0.5rem;
}

/* Maintenance Mode Styles */
.launch-countdown__maintenance {
  margin: 2rem 0;
}

/* CTA Section */
.launch-countdown__cta {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid var(--light-gray);
}

.launch-countdown__cta-text {
  font-size: var(--basefont);
  color: var(--dark-gray);
  margin-bottom: 1.5rem;
  font-weight: 500;
}

.launch-countdown__social {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  background: var(--primary-color);
  color: var(--white);
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 1.2rem;
}

.social-link:hover {
  background: var(--second-primary-color);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Background Animation */



@keyframes float {
  0%, 100% {
    transform: translate(0, 0) rotate(0deg);
  }
  33% {
    transform: translate(30px, -30px) rotate(120deg);
  }
  66% {
      transform: translate(-20px, 20px) rotate(240deg);
    }
  }

  /* Spinner for launch state */
  .launch-countdown__spinner {
    margin: 2rem 0;
  }

  .spinner {
    width: 50px;
    height: 50px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--primary-color);
    border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .launch-countdown__container {
    padding: 1rem;
  }

  .launch-countdown__content {
    padding: 2rem 1.5rem;
  }

  .launch-countdown__title {
    font-size: var(--heading3);
    flex-direction: column;
    gap: 0.5rem;
  }

  .launch-countdown__timer {
    gap: 0.5rem;
  }

  .timer-unit {
    min-width: 80px;
    padding: 1rem 0.5rem;
  }

  .timer-number {
    font-size: 2rem;
  }

  .timer-separator {
    display: none;
  }

  .launch-countdown__social {
    gap: 0.75rem;
  }

  .social-link {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .launch-countdown__title {
    font-size: var(--heading4);
  }

  .timer-unit {
    min-width: 70px;
    padding: 0.75rem 0.25rem;
  }

  .timer-number {
    font-size: 1.75rem;
  }

  .timer-label {
    font-size: var(--extrasmallfont);
  }
}
