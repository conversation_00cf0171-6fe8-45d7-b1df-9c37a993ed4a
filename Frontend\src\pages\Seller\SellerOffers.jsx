import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Link } from "react-router-dom";
import { getSellerOffers, setPage } from "../../redux/slices/offerSlice";
import SellerLayout from "../../components/seller/SellerLayout";
import Table from "../../components/common/Table";
import Pagination from "../../components/common/Pagination";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import OfferAcceptanceModal from "../../components/seller/OfferAcceptanceModal";
import { FaCheck } from "react-icons/fa";
import { FiEye } from "react-icons/fi";
import { MdLocalOffer } from "react-icons/md";
import "../../styles/SellerOffers.css";
import {
  getSmartFileUrl,
  getPlaceholderImage,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth
} from "../../utils/constants";
import ThumbnailImage from "../../components/common/ThumbnailImage";
import { formatStandardDate } from "../../utils/dateValidation";

const SellerOffers = () => {
  const dispatch = useDispatch();
  const { sellerOffers, isLoading, isError, error, pagination } = useSelector(
    (state) => state.offer
  );
  const [showOfferModal, setShowOfferModal] = useState(false);
  const [selectedOffer, setSelectedOffer] = useState(null);

  useEffect(() => {
    dispatch(
      getSellerOffers({ page: pagination.page, limit: pagination.limit })
    );
  }, [dispatch, pagination.page]);

  const handleOfferAction = (offer) => {
    setSelectedOffer(offer);
    setShowOfferModal(true);
  };

  const closeOfferModal = () => {
    setShowOfferModal(false);
    setSelectedOffer(null);
  };

  const handleOfferProcessed = () => {
    // Refresh offers after acceptance/rejection
    dispatch(
      getSellerOffers({ page: pagination.page, limit: pagination.limit })
    );
  };

  const handleRetry = () => {
    dispatch(
      getSellerOffers({ page: pagination.page, limit: pagination.limit })
    );
  };

  const handlePageChange = (newPage) => {
    dispatch(setPage(newPage));
    dispatch(
      getSellerOffers({ page: newPage, limit: pagination.limit })
    );
    window.scrollTo(0, 0);
  };

  const getStatusBadge = (status) => {
    const statusClasses = {
      Pending: "status-pending",
      Accepted: "status-accepted",
      Rejected: "status-rejected",
      Cancelled: "status-cancelled",
      Expired: "status-expired",
    };

    return (
      <span className={`status-badge ${statusClasses[status] || ""}`}>
        {status}
      </span>
    );
  };

  const formatDate = (dateString) => {
    return formatStandardDate(dateString);
  };

  const formatPrice = (price) => {
    return `$${parseFloat(price).toFixed(2)}`;
  };

  const columns = [
    {
      label: "Content",
      key: "content",
      render: (offer) => (
        <div className="content-info">
          <div className="content-thumbnail">
            {offer.content?._id || offer.content?.thumbnailUrl ? (
              <ThumbnailImage
                contentId={offer.content?._id}
                thumbnailUrl={offer.content?.thumbnailUrl}
                alt={offer.content.title}
                placeholderWidth={80}
                placeholderHeight={80}
                placeholderText="No image"
              />
            ) : (
              <div className="no-thumbnail">
                <MdLocalOffer />
              </div>
            )}
          </div>
          <div className="content-details">
            <h4 className="content-title">
              {offer.content?.title || "Untitled"}
            </h4>
            <p className="content-sport">{offer.content?.sport || "N/A"}</p>
          </div>
        </div>
      ),
    },
    {
      label: "Buyer",
      key: "buyer",
      render: (offer) => (
        <div className="buyer-info">
          <span className="buyer-name">
            {offer.buyer?.firstName} {offer.buyer?.lastName}
          </span>
        </div>
      ),
    },
    {
      label: "Offer Amount",
      key: "amount",
      render: (offer) => (
        <span className="offer-amount">{formatPrice(offer.amount)}</span>
      ),
    },
    {
      label: "Status",
      key: "status",
      render: (offer) => getStatusBadge(offer.status),
    },
    {
      label: "Date",
      key: "createdAt",
      render: (offer) => (
        <span className="offer-date">{formatDate(offer.createdAt)}</span>
      ),
    },
    {
      label: "Actions",
      key: "actions",
      render: (offer) => (
        <div className="action-buttons">
          <Link
            to={`/seller/offer-details/${offer._id}`}
            className="action-btn btn-icon view-btn"
          >
            <FiEye />
          </Link>
          {offer.status === "Pending" && (
            <button
              className="btn-icon btn-manage"
              onClick={() => handleOfferAction(offer)}
              title="Manage Offer"
            >
              <FaCheck />
            </button>
          )}
        </div>
      ),
    },
  ];

  return (
    <SellerLayout>
      <div className="SellerOffers">
        <div className="page-header">
          <p>Manage offers from buyers for your content</p>
        </div>

        {isLoading ? (
          <LoadingSkeleton type="table" rows={5} />
        ) : isError ? (
          <ErrorDisplay
            error={error}
            onRetry={handleRetry}
            title="Failed to load offers"
          />
        ) : sellerOffers && sellerOffers.length > 0 ? (
          <>
            <div className="offers-summary">
              <p>
                You have received {pagination.total} offer
                {pagination.total !== 1 ? "s" : ""}
              </p>
            </div>
            <Table
              columns={columns}
              data={sellerOffers}
              className="offers-table"
            />
            {pagination.totalPages > 1 && (
              <div className="pagination-container">
                <Pagination
                  currentPage={pagination.page}
                  totalPages={pagination.totalPages}
                  onPageChange={handlePageChange}
                />
              </div>
            )}
          </>
        ) : (
          <div className="no-offers">
            <MdLocalOffer className="no-offers-icon" />
            <h3>No Offers Yet</h3>
            <p>
              You haven't received any offers yet. Keep creating great content
              to attract buyers!
            </p>
            <Link
              to="/seller/my-sports-strategies/add"
              className="btn-primary text-decoration-none"
            >
              Add New Content
            </Link>
          </div>
        )}

        {/* Offer Acceptance Modal */}
        <OfferAcceptanceModal
          isOpen={showOfferModal}
          onClose={closeOfferModal}
          offer={selectedOffer}
          onOfferProcessed={handleOfferProcessed}
        />
      </div>
    </SellerLayout>
  );
};

export default SellerOffers;
