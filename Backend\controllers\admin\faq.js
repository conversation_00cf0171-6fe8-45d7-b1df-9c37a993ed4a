const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const FAQ = require('../../models/FAQ');

// @desc    Get all FAQs (Admin)
// @route   GET /api/admin/faqs
// @access  Private/Admin
exports.getAllFAQs = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      category,
      search,
      sortBy = 'order',
      sortOrder = 'asc'
    } = req.query;

    // Build query
    const query = {};
    
    if (status) {
      query.status = status;
    }
    
    if (category) {
      query.category = category;
    }
    
    if (search) {
      query.$or = [
        { question: { $regex: search, $options: 'i' } },
        { answer: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    
    // Get FAQs with pagination
    const faqs = await FAQ.find(query)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email')
      .sort(sort)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await FAQ.countDocuments(query);
    const totalPages = Math.ceil(total / parseInt(limit));

    res.status(200).json({
      success: true,
      data: faqs,
      pagination: {
        current: parseInt(page),
        pages: totalPages,
        total,
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('Get all FAQs error:', error);
    next(new ErrorResponse('Failed to fetch FAQs', 500));
  }
};

// @desc    Get FAQ by ID
// @route   GET /api/admin/faqs/:id
// @access  Private/Admin
exports.getFAQById = async (req, res, next) => {
  try {
    const faq = await FAQ.findById(req.params.id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    if (!faq) {
      return next(new ErrorResponse('FAQ not found', 404));
    }

    res.status(200).json({
      success: true,
      data: faq
    });
  } catch (error) {
    console.error('Get FAQ by ID error:', error);
    next(new ErrorResponse('Failed to fetch FAQ', 500));
  }
};

// @desc    Create FAQ
// @route   POST /api/admin/faqs
// @access  Private/Admin
exports.createFAQ = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { question, answer, category, status, order } = req.body;

    // If no order specified, get next order number
    const faqOrder = order !== undefined ? order : await FAQ.getNextOrder();

    const faq = await FAQ.create({
      question,
      answer,
      category: category || 'general',
      status: status || 'active',
      order: faqOrder,
      createdBy: req.user.id,
      updatedBy: req.user.id
    });

    const populatedFAQ = await FAQ.findById(faq._id)
      .populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    res.status(201).json({
      success: true,
      data: populatedFAQ,
      message: 'FAQ created successfully'
    });
  } catch (error) {
    console.error('Create FAQ error:', error);
    next(new ErrorResponse('Failed to create FAQ', 500));
  }
};

// @desc    Update FAQ
// @route   PUT /api/admin/faqs/:id
// @access  Private/Admin
exports.updateFAQ = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    let faq = await FAQ.findById(req.params.id);

    if (!faq) {
      return next(new ErrorResponse('FAQ not found', 404));
    }

    const { question, answer, category, status, order } = req.body;

    const updatedFAQ = await FAQ.findByIdAndUpdate(
      req.params.id,
      {
        question,
        answer,
        category,
        status,
        order,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    res.status(200).json({
      success: true,
      data: updatedFAQ,
      message: 'FAQ updated successfully'
    });
  } catch (error) {
    console.error('Update FAQ error:', error);
    next(new ErrorResponse('Failed to update FAQ', 500));
  }
};

// @desc    Delete FAQ
// @route   DELETE /api/admin/faqs/:id
// @access  Private/Admin
exports.deleteFAQ = async (req, res, next) => {
  try {
    const faq = await FAQ.findById(req.params.id);

    if (!faq) {
      return next(new ErrorResponse('FAQ not found', 404));
    }

    await FAQ.findByIdAndDelete(req.params.id);

    res.status(200).json({
      success: true,
      message: 'FAQ deleted successfully'
    });
  } catch (error) {
    console.error('Delete FAQ error:', error);
    next(new ErrorResponse('Failed to delete FAQ', 500));
  }
};

// @desc    Bulk delete FAQs
// @route   POST /api/admin/faqs/bulk-delete
// @access  Private/Admin
exports.bulkDeleteFAQs = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { faqIds } = req.body;

    if (!Array.isArray(faqIds) || faqIds.length === 0) {
      return next(new ErrorResponse('FAQ IDs array is required', 400));
    }

    const result = await FAQ.deleteMany({ _id: { $in: faqIds } });

    res.status(200).json({
      success: true,
      message: `${result.deletedCount} FAQs deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (error) {
    console.error('Bulk delete FAQs error:', error);
    next(new ErrorResponse('Failed to delete FAQs', 500));
  }
};

// @desc    Update FAQ status
// @route   PATCH /api/admin/faqs/:id/status
// @access  Private/Admin
exports.updateFAQStatus = async (req, res, next) => {
  try {
    const { status } = req.body;

    if (!['active', 'inactive'].includes(status)) {
      return next(new ErrorResponse('Invalid status. Must be active or inactive', 400));
    }

    const faq = await FAQ.findById(req.params.id);

    if (!faq) {
      return next(new ErrorResponse('FAQ not found', 404));
    }

    const updatedFAQ = await FAQ.findByIdAndUpdate(
      req.params.id,
      {
        status,
        updatedBy: req.user.id,
        updatedAt: new Date()
      },
      { new: true, runValidators: true }
    ).populate('createdBy', 'firstName lastName email')
      .populate('updatedBy', 'firstName lastName email');

    res.status(200).json({
      success: true,
      data: updatedFAQ,
      message: `FAQ ${status === 'active' ? 'activated' : 'deactivated'} successfully`
    });
  } catch (error) {
    console.error('Update FAQ status error:', error);
    next(new ErrorResponse('Failed to update FAQ status', 500));
  }
};

// @desc    Reorder FAQs
// @route   PUT /api/admin/faqs/reorder
// @access  Private/Admin
exports.reorderFAQs = async (req, res, next) => {
  try {
    const { faqOrders } = req.body;

    if (!Array.isArray(faqOrders)) {
      return next(new ErrorResponse('FAQ orders array is required', 400));
    }

    // Update each FAQ's order
    const updatePromises = faqOrders.map(({ id, order }) =>
      FAQ.findByIdAndUpdate(id, { order, updatedBy: req.user.id, updatedAt: new Date() })
    );

    await Promise.all(updatePromises);

    res.status(200).json({
      success: true,
      message: 'FAQs reordered successfully'
    });
  } catch (error) {
    console.error('Reorder FAQs error:', error);
    next(new ErrorResponse('Failed to reorder FAQs', 500));
  }
};
