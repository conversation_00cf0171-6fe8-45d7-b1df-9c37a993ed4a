import React from 'react';
import { FaCheckCircle, FaTimes } from 'react-icons/fa';
import '../../styles/ContentSubmissionModal.css';

const ContentSubmissionModal = ({ isOpen, onClose, contentTitle }) => {
  if (!isOpen) return null;

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  return (
    <div className="content-submission-modal-overlay" onClick={handleOverlayClick}>
      <div className="content-submission-modal">
        <div className="content-submission-modal__header">
          <button 
            className="content-submission-modal__close"
            onClick={onClose}
            aria-label="Close modal"
          >
            <FaTimes />
          </button>
        </div>

        <div className="content-submission-modal__content">
          <div className="content-submission-modal__icon">
            <FaCheckCircle />
          </div>

          <h2 className="content-submission-modal__title">
            Content Submitted Successfully!
          </h2>

          <div className="content-submission-modal__message">
            <p>
              Your content <strong>"{contentTitle}"</strong> has been submitted and will be listed after admin approval.
            </p>
            <p>
              You will receive a notification once your content has been reviewed and approved by our team.
            </p>
          </div>

          <div className="content-submission-modal__actions">
            <button 
              className="btn-primary"
              onClick={onClose}
            >
              Got it!
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContentSubmissionModal;
