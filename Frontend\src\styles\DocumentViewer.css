/* Document Viewer Styles */
.document-viewer {
  position: relative;
  width: 100%;
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--white);

  touch-action: auto;
  pointer-events: auto;

  contain: layout style;
}

/* Enhanced Android compatibility */
.document-viewer--android {
  /* Improved Android rendering */
  contain: layout style;
  will-change: auto;
  /* Better isolation */
  isolation: isolate;
}

/* Loading state */
.document-viewer--loading {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.document-viewer__loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--heading4);
  text-align: center;
}

.document-viewer__spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--light-gray);
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--heading6);
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Error state */
.document-viewer--error {
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
}

.document-viewer__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: var(--heading4);
  text-align: center;
}

.document-viewer__error-icon {
  font-size: 3rem;
  color: var(--error-color, #e53e3e);
  margin-bottom: var(--heading6);
}

.document-viewer__error h3 {
  margin: 0 0 var(--smallfont) 0;
  color: var(--error-color, #e53e3e);
  font-size: var(--heading6);
}

.document-viewer__error p {
  margin: 0 0 var(--heading6) 0;
  color: var(--text-color);
}

/* Mobile PDF fallback */
.document-viewer--mobile-pdf {
  background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
}

.document-viewer__mobile-fallback {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--heading4);
}

.document-viewer__mobile-header {
  display: flex;
  align-items: center;
  gap: var(--heading6);
  margin-bottom: var(--heading4);
  flex: 1;
}

.document-viewer__mobile-icon {
  font-size: 4rem;
  color: #dc3545;
  flex-shrink: 0;
}

.document-viewer__mobile-info h3 {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading5);
  color: var(--text-color);
}

.document-viewer__mobile-info p {
  margin: 0 0 var(--smallfont) 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
}

.document-viewer__mobile-note {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  font-style: italic;
  color: var(--primary-color);
  font-weight: 500;
}

.document-viewer__mobile-note-icon {
  font-size: var(--smallfont);
}

.document-viewer__mobile-actions {
  display: flex;
  gap: var(--smallfont);
  margin-top: auto;
}

.document-viewer__mobile-open-btn,
.document-viewer__mobile-preview-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--smallfont) var(--heading6);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
  min-height: 48px; /* Touch-friendly */
}

.document-viewer__mobile-open-btn.primary,
.document-viewer__mobile-open-btn:not(.secondary) {
  background: var(--primary-color);
  color: white;
}

.document-viewer__mobile-open-btn.primary:hover,
.document-viewer__mobile-open-btn:not(.secondary):hover {
  background: var(--primary-dark, #0056b3);
  transform: translateY(-1px);
}

.document-viewer__mobile-preview-btn.secondary {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.document-viewer__mobile-preview-btn.secondary:hover {
  background: var(--primary-color);
  color: white;
}

/* Mobile helper button */
.document-viewer__mobile-helper {
  position: absolute;
  top: var(--smallfont);
  right: var(--smallfont);
  z-index: 10;
}

.document-viewer__mobile-helper-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  font-size: var(--basefont);
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.document-viewer__mobile-helper-btn:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

.document-viewer__preview-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: var(--heading4);
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.document-viewer__header {
  display: flex;
  align-items: flex-start;
  gap: var(--heading6);
  margin-bottom: var(--heading4);
}

.document-viewer__icon-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  border-radius: var(--border-radius);
  background: var(--white);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.document-viewer__icon {
  font-size: 2.5rem;
}

.document-viewer__icon--word {
  color: #2b579a;
}

.document-viewer__icon--excel {
  color: #217346;
}

.document-viewer__icon--pdf {
  color: #dc3545;
}

.document-viewer__icon--text {
  color: #6c757d;
}

.document-viewer__icon--default {
  color: var(--dark-gray);
}

.document-viewer__info {
  flex: 1;
  min-width: 0;
}

.document-viewer__title {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--heading6);
  font-weight: 600;
  color: var(--text-color);
  line-height: 1.3;
  word-break: break-word;
}

.document-viewer__type {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--primary-color);
}

.document-viewer__filename {
  margin: 0 0 var(--smallfont) 0;
  font-size: var(--smallfont);
  color: var(--dark-gray);
  word-break: break-all;
  font-family: "Courier New", monospace;
}

.document-viewer__extension {
  margin: 0;
  font-size: var(--smallfont);
  color: var(--medium-gray);
  font-weight: 500;
  text-transform: uppercase;
}

.document-viewer__content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: var(--heading4);
  background: var(--white);
  border-radius: var(--border-radius);
  border: 2px dashed var(--light-gray);
}

.document-viewer__preview-message {
  margin-bottom: var(--heading4);
  width: 100%;
}

.document-viewer__preview-message p {
  margin: 0 0 var(--smallfont) 0;
  color: var(--dark-gray);
  font-size: var(--basefont);
  line-height: 1.5;
}

.document-viewer__preview-message p:last-child {
  margin-bottom: 0;
}

/* Mobile-specific styles */
@media (max-width: 768px) {
  .document-viewer {
    /* Ensure proper mobile interaction */
    touch-action: auto !important;
    pointer-events: auto !important;
  }

  .document-viewer__preview-card {
    padding: var(--heading6);
  }

  .document-viewer__header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: var(--basefont);
  }

  .document-viewer__icon-container {
    width: 60px;
    height: 60px;
  }

  .document-viewer__icon {
    font-size: 2rem;
  }

  .document-viewer__title {
    font-size: var(--basefont);
  }

  .document-viewer__content {
    padding: var(--heading6);
  }

  /* Enhanced Android mobile rendering */
  .document-viewer--android {
    /* Better Android mobile performance */
    contain: layout style paint;
    transform: translateZ(0);
  }

  /* Mobile actions within preview message */
  .document-viewer__mobile-actions {
    margin-top: var(--heading6);
    width: 100%;
  }

  .document-viewer__mobile-open-btn {
    width: 100%;
    min-height: 48px;
    font-size: var(--basefont);
    font-weight: 600;
  }

  /* Mobile fallback adjustments */
  .document-viewer__mobile-fallback {
    padding: var(--heading6);
  }

  .document-viewer__mobile-header {
    flex-direction: column;
    text-align: center;
    gap: var(--basefont);
  }

  .document-viewer__mobile-icon {
    font-size: 3rem;
  }

  .document-viewer__mobile-actions {
    flex-direction: column;
    gap: var(--smallfont);
  }

  /* Mobile helper button adjustments */
  .document-viewer__mobile-helper {
    top: var(--extrasmallfont);
    right: var(--extrasmallfont);
  }

  .document-viewer__mobile-helper-btn {
    width: 36px;
    height: 36px;
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .document-viewer__mobile-icon {
    font-size: 2.5rem;
  }

  .document-viewer__mobile-info h3 {
    font-size: var(--heading6);
  }

  .document-viewer__mobile-info p {
    font-size: var(--smallfont);
  }

  .document-viewer__mobile-open-btn,
  .document-viewer__mobile-preview-btn {
    padding: var(--smallfont);
    font-size: var(--smallfont);
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .document-viewer {
    border: 2px solid var(--text-color);
  }

  .document-viewer__mobile-open-btn.primary {
    background: var(--text-color);
    color: var(--white);
  }

  .document-viewer__mobile-preview-btn.secondary {
    border-color: var(--text-color);
    color: var(--text-color);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .document-viewer__spinner {
    animation: none;
  }

  .document-viewer__mobile-open-btn,
  .document-viewer__mobile-preview-btn,
  .document-viewer__mobile-helper-btn {
    transition: none;
  }

  .document-viewer__mobile-open-btn:hover,
  .document-viewer__mobile-preview-btn:hover,
  .document-viewer__mobile-helper-btn:hover {
    transform: none;
  }
}

/* Focus styles for accessibility */
.document-viewer__mobile-open-btn:focus,
.document-viewer__mobile-preview-btn:focus,
.document-viewer__mobile-helper-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .document-viewer__mobile-helper,
  .document-viewer__mobile-actions {
    display: none;
  }
}

/* Error actions */
.document-viewer__error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  margin-top: 16px;
}

.document-viewer__download-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.document-viewer__download-btn:hover {
  background: #218838;
  transform: translateY(-1px);
}
