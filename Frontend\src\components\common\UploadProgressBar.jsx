import React from 'react';
import { FaTimes, FaRedo, FaPause, FaPlay } from 'react-icons/fa';
import './UploadProgressBar.css';

const UploadProgressBar = ({
  progress = 0,
  isVisible = false,
  fileName = '',
  uploadType = 'file',
  uploadStats = {},
  error = null,
  isRetrying = false,
  canRetry = false,
  canCancel = false,
  onRetry = null,
  onCancel = null,
  onPause = null,
  onResume = null,
  isPaused = false
}) => {
  if (!isVisible) return null;

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatTime = (seconds) => {
    if (!seconds || seconds === Infinity) return '--';
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  };

  const formatSpeed = (bytesPerSecond) => {
    if (!bytesPerSecond) return '--';
    return `${formatBytes(bytesPerSecond)}/s`;
  };

  return (
    <div className="upload-progress-overlay">
      <div className="upload-progress-container">
        <div className="upload-progress-header">
          <div className="upload-progress-title-section">
            <h4 className="upload-progress-title">
              {error ? 'Upload Failed' :
               isRetrying ? 'Retrying Upload...' :
               isPaused ? 'Upload Paused' :
               progress >= 100 ? 'Processing...' :
               `Uploading ${uploadType}...`}
            </h4>
            <div className="upload-progress-controls">
              {canCancel && onCancel && (
                <button
                  className="upload-control-btn upload-cancel-btn"
                  onClick={onCancel}
                  title="Cancel Upload"
                >
                  <FaTimes />
                </button>
              )}
            </div>
          </div>
          {fileName && (
            <p className="upload-progress-filename">{fileName}</p>
          )}
        </div>

        <div className="upload-progress-bar-container">
          <div className="upload-progress-bar">
            <div
              className={`upload-progress-fill ${error ? 'upload-progress-error' : ''} ${isPaused ? 'upload-progress-paused' : ''}`}
              style={{ width: `${progress}%` }}
            />
          </div>
          <span className="upload-progress-percentage">
            {Math.round(progress)}%
          </span>
        </div>

        {/* Upload Statistics */}
        {uploadStats && Object.keys(uploadStats).length > 0 && (
          <div className="upload-progress-stats">
            <div className="upload-stat">
              <span className="upload-stat-label">Speed:</span>
              <span className="upload-stat-value">{formatSpeed(uploadStats.speed)}</span>
            </div>
            <div className="upload-stat">
              <span className="upload-stat-label">ETA:</span>
              <span className="upload-stat-value">{formatTime(uploadStats.eta)}</span>
            </div>
            <div className="upload-stat">
              <span className="upload-stat-label">Size:</span>
              <span className="upload-stat-value">
                {formatBytes(uploadStats.uploadedBytes || 0)} / {formatBytes(uploadStats.totalBytes || 0)}
              </span>
            </div>
            {uploadStats.uploadedChunks !== undefined && (
              <div className="upload-stat">
                <span className="upload-stat-label">Chunks:</span>
                <span className="upload-stat-value">
                  {uploadStats.uploadedChunks} / {uploadStats.totalChunks}
                </span>
              </div>
            )}
          </div>
        )}

        <div className="upload-progress-status">
          {error ? (
            <div className="upload-error-section">
              <p className="upload-progress-message upload-progress-error">
                {error}
              </p>
              {canRetry && onRetry && (
                <button
                  className="upload-retry-btn"
                  onClick={onRetry}
                  disabled={isRetrying}
                >
                  <FaRedo className={isRetrying ? 'spinning' : ''} />
                  {isRetrying ? 'Retrying...' : 'Retry Upload'}
                </button>
              )}
            </div>
          ) : isPaused ? (
            <div className="upload-pause-section">
              <p className="upload-progress-message">
                Upload paused. Click resume to continue.
              </p>
              {onResume && (
                <button className="upload-resume-btn" onClick={onResume}>
                  <FaPlay />
                  Resume Upload
                </button>
              )}
            </div>
          ) : progress < 100 ? (
            <div className="upload-active-section">
              <p className="upload-progress-message">
                Please wait while your {uploadType} is being uploaded...
              </p>
              {onPause && (
                <button className="upload-pause-btn" onClick={onPause}>
                  <FaPause />
                  Pause
                </button>
              )}
            </div>
          ) : (
            <p className="upload-progress-message upload-progress-complete">
              Upload completed! Processing...
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default UploadProgressBar;
