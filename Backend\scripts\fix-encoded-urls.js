/**
 * Migration script to fix encoded S3 URLs in existing content
 * This script will find content with encoded S3 URLs and fix them
 */

require('dotenv').config();
const mongoose = require('mongoose');
const Content = require('../models/Content');

// Connect to MongoDB
const connectDB = async () => {
  try {
    // Try both possible environment variable names
    const mongoUri = process.env.MONGODB_URI || process.env.MONGO_URI;

    if (!mongoUri) {
      throw new Error('MongoDB URI not found. Please set MONGODB_URI or MONGO_URI in your .env file');
    }

    await mongoose.connect(mongoUri);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Function to check if a URL has encoding issues
const hasEncodingIssues = (url) => {
  if (!url || typeof url !== 'string') return false;

  // Check if the URL contains encoded slashes or other encoded characters
  // Also check for double encoding like %252F
  return url.includes('%2F') || url.includes('%2C') || url.includes('%20') || url.includes('%252F');
};

// Function to fix encoded URL
const fixEncodedUrl = (url) => {
  if (!url || typeof url !== 'string') return url;

  try {
    const urlObj = new URL(url);

    // If it's an S3 URL with encoding issues
    if (urlObj.hostname.includes('amazonaws.com') && hasEncodingIssues(url)) {
      // Extract the encoded path
      let encodedPath = urlObj.pathname;

      // Handle double encoding by decoding multiple times if needed
      let decodedPath = encodedPath;
      let previousPath = '';

      // Keep decoding until no more changes occur (handles multiple levels of encoding)
      while (decodedPath !== previousPath && decodedPath.includes('%')) {
        previousPath = decodedPath;
        decodedPath = decodeURIComponent(decodedPath);
      }

      // Reconstruct the URL with clean path
      const bucketName = process.env.AWS_BUCKET_NAME || 'xosports';
      const region = process.env.AWS_REGION || 'us-east-1';
      const cleanKey = decodedPath.startsWith('/') ? decodedPath.substring(1) : decodedPath;

      const fixedUrl = `https://${bucketName}.s3.${region}.amazonaws.com/${cleanKey}`;

      console.log(`🔧 Fixed URL:`);
      console.log(`   Old: ${url}`);
      console.log(`   New: ${fixedUrl}`);

      return fixedUrl;
    }

    return url;
  } catch (error) {
    console.error('❌ Error fixing URL:', url, error.message);
    return url;
  }
};

// Main migration function
const fixEncodedUrls = async () => {
  try {
    console.log('🔍 Searching for content with encoded S3 URLs...\n');
    
    // Find all content with S3 URLs that might have encoding issues
    const allContent = await Content.find({
      $or: [
        { fileUrl: { $regex: 'amazonaws.com.*%' } },
        { previewUrl: { $regex: 'amazonaws.com.*%' } },
        { thumbnailUrl: { $regex: 'amazonaws.com.*%' } }
      ]
    });
    
    console.log(`📊 Found ${allContent.length} content items with potential encoding issues\n`);
    
    if (allContent.length === 0) {
      console.log('✅ No content with encoding issues found!');
      return;
    }
    
    let fixedCount = 0;
    
    for (const content of allContent) {
      let needsUpdate = false;
      const updates = {};
      
      // Check and fix fileUrl
      if (hasEncodingIssues(content.fileUrl)) {
        updates.fileUrl = fixEncodedUrl(content.fileUrl);
        needsUpdate = true;
      }
      
      // Check and fix previewUrl
      if (hasEncodingIssues(content.previewUrl)) {
        updates.previewUrl = fixEncodedUrl(content.previewUrl);
        needsUpdate = true;
      }
      
      // Check and fix thumbnailUrl
      if (hasEncodingIssues(content.thumbnailUrl)) {
        updates.thumbnailUrl = fixEncodedUrl(content.thumbnailUrl);
        needsUpdate = true;
      }
      
      if (needsUpdate) {
        console.log(`📝 Updating content: ${content.title} (ID: ${content._id})`);

        // Show what's being updated
        if (updates.fileUrl) {
          console.log(`   📄 Fixed fileUrl`);
        }
        if (updates.previewUrl) {
          console.log(`   🎬 Fixed previewUrl`);
        }
        if (updates.thumbnailUrl) {
          console.log(`   🖼️  Fixed thumbnailUrl`);
        }

        await Content.findByIdAndUpdate(content._id, updates);
        fixedCount++;
        console.log(`   ✅ Updated successfully\n`);
      }
    }
    
    console.log(`\n✅ Migration completed!`);
    console.log(`📊 Fixed ${fixedCount} content items`);
    
  } catch (error) {
    console.error('❌ Migration error:', error);
  }
};

// Run the migration
const runMigration = async () => {
  console.log('🚀 Starting S3 URL encoding fix migration...\n');
  
  await connectDB();
  await fixEncodedUrls();
  
  console.log('\n🎉 Migration completed successfully!');
  console.log('💡 Tip: Test your video uploads to ensure they work correctly now.');
  
  process.exit(0);
};

// Handle script execution
if (require.main === module) {
  runMigration().catch(error => {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  });
}

module.exports = { fixEncodedUrls, hasEncodingIssues, fixEncodedUrl };
