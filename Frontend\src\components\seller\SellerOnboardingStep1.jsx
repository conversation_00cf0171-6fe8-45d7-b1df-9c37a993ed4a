import React, { useState } from "react";
import { RiDeleteBin6Line } from "react-icons/ri";
import { useDispatch } from 'react-redux';
import { uploadProfileImage, updateCurrentUser } from '../../redux/slices/authSlice';
import { showSuccess, showError } from '../../utils/toast';
import "./SellerOnboardingStep1.css";

const SellerOnboardingStep1 = ({
  formData,
  onInputChange,
  onExperienceChange,
  onAddExperience,
  onRemoveExperience,
  onNext,
  fieldErrors,
}) => {
  const dispatch = useDispatch();
  const [previewUrl, setPreviewUrl] = useState(formData.profileImage || null);
  const [imageError, setImageError] = useState("");
  const [isUploading, setIsUploading] = useState(false);

  // Supported image formats
  const SUPPORTED_IMAGE_FORMATS = ["jpg", "jpeg", "png"];
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  // Validate image file
  const validateImageFile = (file) => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return "File size must be less than 10MB";
    }

    // Check file type
    const fileExtension = file.name.split(".").pop().toLowerCase();
    if (!SUPPORTED_IMAGE_FORMATS.includes(fileExtension)) {
      return `Unsupported file format. Please use: ${SUPPORTED_IMAGE_FORMATS.join(
        ", "
      ).toUpperCase()}`;
    }

    // Check MIME type
    const supportedMimeTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
    ];
    if (!supportedMimeTypes.includes(file.type)) {
      return `Invalid file type. Please select a valid image file.`;
    }

    return null; // No errors
  };

  const handleFileSelect = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Clear previous errors
      setImageError("");

      // Validate the file
      const validationError = validateImageFile(file);
      if (validationError) {
        setImageError(validationError);
        // Clear the file input
        e.target.value = "";
        return;
      }

      try {
        setIsUploading(true);

        // Upload the image
        const response = await dispatch(uploadProfileImage(file)).unwrap();

        if (response.success) {
          // Update form data with the image URL
          onInputChange("profileImage", response.data.fileUrl);

          // Update preview
          setPreviewUrl(response.data.fileUrl);

          // Get current user data from localStorage
          const userData = JSON.parse(localStorage.getItem('xosportshub_user'));

          // Immediately update the user's profile with the new image URL and name
          await dispatch(updateCurrentUser({
            profileImage: response.data.fileUrl,
            firstName: userData.firstName,
            lastName: userData.lastName
          })).unwrap();

          showSuccess('Profile image uploaded successfully');
        }
      } catch (error) {
        const errorMessage = error.message || 'Failed to upload profile image';
        setImageError(errorMessage);
        showError(errorMessage);
      } finally {
        setIsUploading(false);
      }
    }
  };

  return (
    <div className="seller-onboarding-step1-container max-container">
      {/* Progress Bar */}
      <div className="progress-bar">
        <div className="step active">1</div>
        <div className="progress-line" />
        <div className="step">2</div>
        <div className="progress-line" />
        <div className="step">3</div>
      </div>

      <div className="form-grid">
        {/* Description Section */}
        <div className="description-section">
          <div className="section-title required-field">Description</div>
          <div className="description-box">
            <textarea
              className={`description-textarea ${fieldErrors?.description ? "error" : ""
                }`}
              placeholder="Write Description.."
              rows={3}
              value={formData.description}
              onChange={(e) => onInputChange("description", e.target.value)}
            />
            {fieldErrors?.description && (
              <div className="field-error">{fieldErrors.description}</div>
            )}
          </div>
        </div>

        {/* Profile Pic & Experience Section */}
        <div className="profile-experience-grid">
          {/* Profile Pic */}
          <div className="profile-pic-section">
            <div className="section-title required-field">Profile Pic</div>
            <div className="avatar-upload">
              <div className="avatar-placeholder">
                {previewUrl ? (
                  <img
                    src={`${import.meta.env.VITE_IMAGE_BASE_URL}${previewUrl}`}
                    alt="Profile"
                    className="avatar-image"
                  />
                ) : (
                  <svg
                    width="64"
                    height="64"
                    viewBox="0 0 64 64"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <circle cx="32" cy="32" r="32" fill="var(--light-gray)" />
                    <ellipse cx="32" cy="27" rx="12" ry="12" fill="#fff" />
                    <ellipse cx="32" cy="50" rx="16" ry="10" fill="#fff" />
                  </svg>
                )}
              </div>
              <input
                type="file"
                id="profileImageInput"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                onChange={handleFileSelect}
                style={{ display: "none" }}
              />
              <div className="upload-buttons">
                <button
                  type="button"
                  className="btn btn-outline upload-btn"
                  onClick={() => document.getElementById("profileImageInput").click()}
                  disabled={isUploading}
                >
                  {isUploading ? 'Uploading...' : 'Choose Photo'}
                </button>
              </div>
              {/* Display supported formats */}
              <div className="upload-info">
                <small className="upload-format-info">
                  Supported formats:{" "}
                  {SUPPORTED_IMAGE_FORMATS.join(", ").toUpperCase()} (Max: 10MB)
                </small>
              </div>
              {/* Display validation errors */}
              {imageError && (
                <div className="field-error image-error">{imageError}</div>
              )}
            </div>
          </div>

          {/* Experience */}
          <div className="experience-section">
            <div className="section-title required-field">Experience</div>
            <div className="experience-container">
              {formData.experiences.map((exp, idx) => (
                <div className="experience-row" key={idx}>
                  <div className="experience-row-content">
                    <div>
                      <input
                        type="text"
                        className={`input ${fieldErrors?.experiences && idx === 0 ? "error" : ""
                          } ${fieldErrors?.[`experience_${idx}`]?.schoolName ? "error" : ""}`}
                        placeholder="Enter Experience"
                        value={exp.schoolName}
                        onChange={(e) =>
                          onExperienceChange(idx, "schoolName", e.target.value)
                        }
                      />
                      {fieldErrors?.[`experience_${idx}`]?.schoolName && (
                        <div className="field-error">
                          {fieldErrors[`experience_${idx}`].schoolName}
                        </div>
                      )}
                    </div>
                    <div>
                      <input
                        type="text"
                        className={`input ${fieldErrors?.[`experience_${idx}`]?.position ? "error" : ""}`}
                        placeholder="Enter Position"
                        value={exp.position}
                        onChange={(e) =>
                          onExperienceChange(idx, "position", e.target.value)
                        }
                      />
                      {fieldErrors?.[`experience_${idx}`]?.position && (
                        <div className="field-error">
                          {fieldErrors[`experience_${idx}`].position}
                        </div>
                      )}
                    </div>
                    <div className="year-fields">
                      <div>
                        <input
                          type="text"
                          className={`input year-input ${fieldErrors?.experienceYears?.[idx]?.fromYear
                            ? "error"
                            : ""
                            }`}
                          placeholder="From Year"
                          value={exp.fromYear}
                          onChange={(e) =>
                            onExperienceChange(idx, "fromYear", e.target.value)
                          }
                        />
                        {fieldErrors?.experienceYears?.[idx]?.fromYear && (
                          <div className="field-error">
                            {fieldErrors.experienceYears[idx].fromYear}
                          </div>
                        )}
                      </div>

                      <div>
                        <input
                          type="text"
                          className={`input year-input ${fieldErrors?.experienceYears?.[idx]?.toYear
                            ? "error"
                            : ""
                            }`}
                          placeholder="To Year"
                          value={exp.toYear}
                          onChange={(e) =>
                            onExperienceChange(idx, "toYear", e.target.value)
                          }
                        />
                        {fieldErrors?.experienceYears?.[idx]?.toYear && (
                          <div className="field-error">
                            {fieldErrors.experienceYears[idx].toYear}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* Delete button - only show when there are 2 or more experiences */}
                  {formData.experiences.length > 1 && onRemoveExperience && (
                    <button
                      type="button"
                      className="delete-experience-btn"
                      onClick={() => onRemoveExperience(idx)}
                      title="Remove this experience"
                    >
                      <RiDeleteBin6Line className="delete-icon" />
                    </button>
                  )}
                </div>
              ))}
              {fieldErrors?.experiences && (
                <div className="field-error">{fieldErrors.experiences}</div>
              )}
              <div className="add-more-link" onClick={onAddExperience}>
                + Add More
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Next Button */}
      <div className="next-btn-row">
        <button
          type="button"
          className="btn btn-primary next-btn"
          onClick={onNext}
        >
          Next
        </button>
      </div>
    </div>
  );
};

export default SellerOnboardingStep1;
