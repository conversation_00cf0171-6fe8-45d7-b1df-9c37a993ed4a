import api from './api';
import {
  getUploadConfig,
  isChunkedUploadSupported,
  getOptimalChunkSize,
  CHUNKED_UPLOAD_CONFIG,
  ERROR_CONFIG
} from '../config/uploadConfig';

/**
 * Chunked Upload Service
 * Handles large file uploads by splitting them into smaller chunks
 */

class ChunkedUploadService {
  constructor() {
    this.activeUploads = new Map();
  }

  /**
   * Upload a file using chunked upload
   * @param {File} file - The file to upload
   * @param {string} contentType - Content type (Video, Document, etc.)
   * @param {Function} onProgress - Progress callback (progress, speed, eta)
   * @param {Function} onError - Error callback
   * @param {Function} onRetry - Retry callback
   * @returns {Promise} Upload result
   */
  async uploadFile(file, contentType, onProgress, onError, onRetry) {
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

    try {
      // Check browser support
      if (!isChunkedUploadSupported()) {
        throw new Error('Chunked upload not supported in this browser');
      }

      // Get upload configuration
      const config = getUploadConfig(file, contentType);
      const chunkSize = getOptimalChunkSize(file.size);

      // Calculate chunks
      const totalChunks = Math.ceil(file.size / chunkSize);
      const chunks = this.createChunks(file, totalChunks, chunkSize);

      // Initialize upload session
      const initResponse = await this.initializeUpload(file, totalChunks);
      const serverUploadId = initResponse.data.uploadId;

      // Track upload state
      const uploadState = {
        uploadId,
        serverUploadId,
        file,
        chunks,
        totalChunks,
        uploadedChunks: new Set(),
        failedChunks: new Map(),
        startTime: Date.now(),
        lastProgressTime: Date.now(),
        uploadedBytes: 0,
        onProgress,
        onError,
        onRetry
      };

      this.activeUploads.set(uploadId, uploadState);

      // Upload chunks
      const result = await this.uploadChunks(uploadState);

      // Complete upload
      const finalResult = await this.completeUpload(serverUploadId);

      // Cleanup
      this.activeUploads.delete(uploadId);

      return finalResult;

    } catch (error) {
      console.error('[ChunkedUpload] Upload failed:', error);
      this.activeUploads.delete(uploadId);
      throw error;
    }
  }

  /**
   * Resume a failed upload
   * @param {string} uploadId - Upload ID to resume
   * @returns {Promise} Upload result
   */
  async resumeUpload(uploadId) {
    const uploadState = this.activeUploads.get(uploadId);
    if (!uploadState) {
      throw new Error('Upload session not found');
    }

    try {
      // Get current status from server
      const statusResponse = await this.getUploadStatus(uploadState.serverUploadId);
      const serverProgress = statusResponse.data;

      // Update local state based on server state
      uploadState.uploadedChunks.clear();
      for (let i = 0; i < serverProgress.uploadedChunks; i++) {
        uploadState.uploadedChunks.add(i);
      }

      // Continue uploading remaining chunks
      const result = await this.uploadChunks(uploadState);

      // Complete upload
      const finalResult = await this.completeUpload(uploadState.serverUploadId);

      // Cleanup
      this.activeUploads.delete(uploadId);

      return finalResult;

    } catch (error) {
      console.error('[ChunkedUpload] Resume failed:', error);
      throw error;
    }
  }

  /**
   * Cancel an active upload
   * @param {string} uploadId - Upload ID to cancel
   */
  async cancelUpload(uploadId) {
    const uploadState = this.activeUploads.get(uploadId);
    if (uploadState) {
      // Mark as cancelled locally first
      uploadState.cancelled = true;
      this.activeUploads.delete(uploadId);
      // Call backend to clean up chunks
      try {
        const response = await api.delete(`/content/upload/cancel/${uploadState.serverUploadId}`);
        if (response.data.success) {
        }
      } catch (error) {
        console.error('[ChunkedUpload] Error cancelling upload on server:', error);
        // Don't throw error - local cancellation already happened
      }
    }
  }

  /**
   * Create file chunks
   * @param {File} file - File to chunk
   * @param {number} totalChunks - Total number of chunks
   * @param {number} chunkSize - Size of each chunk
   * @returns {Array} Array of chunk objects
   */
  createChunks(file, totalChunks, chunkSize) {
    const chunks = [];
    for (let i = 0; i < totalChunks; i++) {
      const start = i * chunkSize;
      const end = Math.min(start + chunkSize, file.size);
      const chunk = file.slice(start, end);

      chunks.push({
        index: i,
        data: chunk,
        size: chunk.size,
        start,
        end
      });
    }
    return chunks;
  }

  /**
   * Initialize upload session on server
   */
  async initializeUpload(file, totalChunks) {
    const response = await api.post('/content/upload/init', {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      totalChunks
    });
    return response.data;
  }

  /**
   * Upload all chunks with retry logic
   */
  async uploadChunks(uploadState) {
    const { chunks, uploadedChunks } = uploadState;

    // Create upload promises for remaining chunks
    const uploadPromises = chunks
      .filter(chunk => !uploadedChunks.has(chunk.index))
      .map(chunk => this.uploadChunkWithRetry(uploadState, chunk));

    // Upload chunks sequentially to avoid overwhelming the connection
    // This prevents timeouts caused by too many concurrent uploads
    const results = [];

    for (const uploadPromise of uploadPromises) {
      try {
        const result = await uploadPromise;
        results.push(result);

        // Update progress after each chunk
        this.updateProgress(uploadState);

        // Check if upload was cancelled
        if (uploadState.cancelled) {
          throw new Error('Upload cancelled by user');
        }

        // Small delay between chunks to prevent overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 100));
      } catch (error) {
        // If a chunk fails, we'll let the retry logic handle it
        console.error(`[ChunkedUpload] Chunk upload failed, will be retried:`, error);
        throw error;
      }
    }

    return results;
  }

  /**
   * Upload a single chunk with retry logic
   */
  async uploadChunkWithRetry(uploadState, chunk) {
    const { serverUploadId, failedChunks, file } = uploadState;
    let retryCount = failedChunks.get(chunk.index) || 0;
    const maxRetries = CHUNKED_UPLOAD_CONFIG.MAX_RETRIES;

    // Get timeout based on file type
    const contentType = this.getContentTypeFromFile(file);
    const config = getUploadConfig(file, contentType);
    const chunkTimeout = config.timeout || CHUNKED_UPLOAD_CONFIG.CHUNK_TIMEOUT;

    while (retryCount < maxRetries) {
      try {
        // Validate uploadId before creating FormData
        if (!serverUploadId) {
          throw new Error(`Missing serverUploadId for chunk ${chunk.index}`);
        }

        const formData = new FormData();
        formData.append('chunk', chunk.data);
        formData.append('uploadId', serverUploadId);
        formData.append('chunkIndex', chunk.index.toString());

        // Verify FormData contains the expected values
        let formDataDebug = {};
        for (let [key, value] of formData.entries()) {
          formDataDebug[key] = key === 'chunk' ? `File data (${Math.round(chunk.size / 1024)}KB)` : value;
        }

        const response = await api.post('/content/upload/chunk', formData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: chunkTimeout, // Use dynamic timeout based on file type and configuration
        });

        // Mark chunk as uploaded
        uploadState.uploadedChunks.add(chunk.index);
        uploadState.uploadedBytes += chunk.size;
        failedChunks.delete(chunk.index);

        return response.data;

      } catch (error) {
        retryCount++;
        failedChunks.set(chunk.index, retryCount);

        // Get detailed error information
        let errorMessage = error.message;
        let isTimeoutError = false;

        if (error.response?.data?.message) {
          errorMessage = error.response.data.message;
        } else if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
          isTimeoutError = true;
          errorMessage = `Upload timeout (${chunkTimeout}ms exceeded)`;
        }

        console.error(`[ChunkedUpload] Chunk ${chunk.index} failed (attempt ${retryCount}/${maxRetries}):`, {
          error: errorMessage,
          isTimeout: isTimeoutError,
          timeout: chunkTimeout,
          status: error.response?.status,
          data: error.response?.data,
          chunkIndex: chunk.index,
          uploadId: serverUploadId
        });

        if (retryCount >= maxRetries) {
          const finalError = isTimeoutError
            ? `Chunk ${chunk.index} failed after ${maxRetries} attempts due to timeouts. Try reducing chunk size or check your internet connection.`
            : `Chunk ${chunk.index} failed after ${maxRetries} attempts: ${errorMessage}`;
          throw new Error(finalError);
        }

        // Exponential backoff delay with configuration
        const retryDelayBase = CHUNKED_UPLOAD_CONFIG.RETRY_DELAY_BASE;
        const delay = retryDelayBase * Math.pow(2, retryCount - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        // Notify about retry
        if (uploadState.onRetry) {
          uploadState.onRetry(chunk.index, retryCount, maxRetries);
        }
      }
    }
  }

  /**
   * Complete the upload on server
   */
  async completeUpload(serverUploadId) {
    const response = await api.post('/content/upload/complete', {
      uploadId: serverUploadId
    });
    return response.data;
  }

  /**
   * Get upload status from server
   */
  async getUploadStatus(serverUploadId) {
    const response = await api.get(`/content/upload/status/${serverUploadId}`);
    return response.data;
  }

  /**
   * Helper method to determine content type from file
   */
  getContentTypeFromFile(file) {
    if (file.type.startsWith('video/')) {
      return 'Video';
    } else if (file.type === 'application/pdf') {
      return 'Document';
    } else if (file.type.startsWith('image/')) {
      return 'Image';
    }
    return 'Document'; // Default fallback
  }

  /**
   * Update progress and calculate upload speed/ETA
   */
  updateProgress(uploadState) {
    const { uploadedChunks, totalChunks, uploadedBytes, file, startTime, onProgress } = uploadState;

    const progress = Math.round((uploadedChunks.size / totalChunks) * 100);
    const currentTime = Date.now();
    const elapsedTime = currentTime - startTime;

    // Calculate upload speed (bytes per second)
    const speed = uploadedBytes / (elapsedTime / 1000);

    // Calculate ETA (estimated time remaining)
    const remainingBytes = file.size - uploadedBytes;
    const eta = speed > 0 ? remainingBytes / speed : 0;

    if (onProgress) {
      onProgress({
        progress,
        uploadedChunks: uploadedChunks.size,
        totalChunks,
        uploadedBytes,
        totalBytes: file.size,
        speed,
        eta,
        elapsedTime
      });
    }
  }

  /**
   * Format bytes to human readable string
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Format time to human readable string
   */
  formatTime(seconds) {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    if (seconds < 3600) return `${Math.round(seconds / 60)}m ${Math.round(seconds % 60)}s`;
    return `${Math.round(seconds / 3600)}h ${Math.round((seconds % 3600) / 60)}m`;
  }
}

// Export singleton instance
export default new ChunkedUploadService();
