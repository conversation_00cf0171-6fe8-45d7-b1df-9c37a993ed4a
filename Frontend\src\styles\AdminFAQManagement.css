/* Admin FAQ Management Styles */
.AdminFAQManagement {
  background: var(--bg-gray);
  min-height: calc(100vh - 70px);
}

/* Header */
.AdminFAQManagement__header {
  margin-bottom: 1rem;
}

.AdminFAQManagement .header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 1rem;
}

.AdminFAQManagement .header-info {
  flex: 1;
}

.AdminFAQManagement .page-title {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.AdminFAQManagement .title-icon {
  color: var(--primary-color);
  font-size: 1.75rem;
}

.AdminFAQManagement .page-subtitle {
  color: var(--text-muted);
  font-size: 1rem;
  line-height: 1.5;
}

.AdminFAQManagement .create-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(var(--primary-rgb), 0.2);
}

.AdminFAQManagement .create-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

/* Filters */
.AdminFAQManagement__filters {
  background: transparent;
  border-radius: 12px;

  margin-bottom: 1.5rem;

  border: none;
}

.AdminFAQManagement .filters-row {
  display: flex;
  gap: 1rem;
  align-items: center;
  flex-wrap: wrap;
}

.AdminFAQManagement .search-box {
  position: relative;
  flex: 1;
  min-width: 250px;
}

.AdminFAQManagement .search-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1rem;
  pointer-events: none;
}

.AdminFAQManagement .search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.3s ease;
}

.AdminFAQManagement .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.AdminFAQManagement .filter-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  flex-wrap: wrap;
}

.AdminFAQManagement .filter-select {
  padding: 0.75rem 1rem;
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  font-size: 0.875rem;
  background: var(--white);
  color: var(--text-dark);
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.AdminFAQManagement .filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.AdminFAQManagement .bulk-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--light-gray);
  margin-top: 1rem;
}

.AdminFAQManagement .selected-count {
  color: var(--text-muted);
  font-size: 0.875rem;
  font-weight: 500;
}

.AdminFAQManagement .bulk-delete-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

/* Table */
.AdminFAQManagement__table {
  background: var(--white);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  border: 1px solid var(--light-gray);
  margin-bottom: 1.5rem;
}

.AdminFAQManagement .faqs-table .faq-question-cell {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.AdminFAQManagement .question-text {
  font-weight: 500;
  color: var(--text-dark);
  line-height: 1.4;
}

.AdminFAQManagement .category-tag {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: capitalize;
  width: fit-content;
}

.AdminFAQManagement .order-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background: var(--light-gray);
  color: var(--text-dark);
  border-radius: 50%;
  font-size: 0.875rem;
  font-weight: 600;
}

.AdminFAQManagement .status-toggle {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.AdminFAQManagement .status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.AdminFAQManagement .status-badge.active {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.AdminFAQManagement .status-badge.inactive {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.AdminFAQManagement .toggle-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.AdminFAQManagement .toggle-btn:hover {
  background: rgba(var(--primary-rgb), 0.1);
}

.AdminFAQManagement .toggle-on {
  color: #16a34a;
  font-size: 1.25rem;
}

.AdminFAQManagement .toggle-off {
  color: #dc2626;
  font-size: 1.25rem;
}

.AdminFAQManagement .actions-column {
  width: 120px;
}

/* No Results */
.AdminFAQManagement .no-results {
  text-align: center;
  padding: 3rem 1rem;
  color: var(--text-muted);
}

.AdminFAQManagement .no-results-icon {
  font-size: 3rem;
  color: var(--light-gray);
  margin-bottom: 1rem;
}

.AdminFAQManagement .no-results h3 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--text-dark);
}

.AdminFAQManagement .no-results p {
  font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .AdminFAQManagement .header-content {
    flex-direction: column;
    align-items: stretch;
  }

  .AdminFAQManagement .create-btn {
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .AdminFAQManagement .page-title {
    font-size: 1.5rem;
  }

  .AdminFAQManagement .title-icon {
    font-size: 1.5rem;
  }

  .AdminFAQManagement .filters-row {
    flex-direction: column;
    align-items: stretch;
  }

  .AdminFAQManagement .search-box {
    min-width: auto;
  }

  .AdminFAQManagement .filter-group {
    justify-content: stretch;
  }

  .AdminFAQManagement .filter-select {
    flex: 1;
    min-width: auto;
  }

  .AdminFAQManagement .bulk-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }

  .AdminFAQManagement .bulk-delete-btn {
    justify-content: center;
  }
}

@media (max-width: 480px) {


  .AdminFAQManagement .page-title {
    font-size: 1.25rem;
  }

  .AdminFAQManagement .create-btn {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }

  .AdminFAQManagement .question-text {
    font-size: 0.875rem;
  }

  .AdminFAQManagement .category-tag {
    font-size: 0.6875rem;
  }
}
