/* FAQ Editor Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}
.FAQEditorModal {
  background: var(--white);
  border-radius: 16px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1001;
}

.FAQEditorModal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--light-gray);
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  color: var(--white);
}

.FAQEditorModal .header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.FAQEditorModal .header-icon {
  font-size: 1.5rem;
  opacity: 0.9;
}

.FAQEditorModal .modal-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.FAQEditorModal .close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--white);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.125rem;
}

.FAQEditorModal .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.FAQEditorModal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.FAQEditorModal .form-group {
  margin-bottom: 1.5rem;
}

.FAQEditorModal .form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.FAQEditorModal .form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.FAQEditorModal .form-label.required::after {
  content: " *";
  color: #ef4444;
}

.FAQEditorModal .form-input,
.FAQEditorModal .form-textarea,
.FAQEditorModal .form-select {
  width: 100%;
  padding: 0.875rem 1rem;
  border: 2px solid var(--light-gray);
  border-radius: 8px;
  font-size: 0.9375rem;
  color: var(--text-dark);
  background: var(--white);
  transition: all 0.3s ease;
  font-family: inherit;
}

.FAQEditorModal .form-input:focus,
.FAQEditorModal .form-textarea:focus,
.FAQEditorModal .form-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-rgb), 0.1);
}

.FAQEditorModal .form-input.error,
.FAQEditorModal .form-textarea.error,
.FAQEditorModal .form-select.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.FAQEditorModal .form-textarea {
  resize: vertical;
  min-height: 80px;
  line-height: 1.6;
}

.FAQEditorModal .form-select {
  cursor: pointer;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1.25rem;
  padding-right: 3rem;
  appearance: none;
}

.FAQEditorModal .form-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
  gap: 1rem;
}

.FAQEditorModal .char-count {
  font-size: 0.8125rem;
  color: var(--text-muted);
}

.FAQEditorModal .error-message {
  font-size: 0.8125rem;
  color: #ef4444;
  font-weight: 500;
}

.FAQEditorModal .form-help {
  font-size: 0.8125rem;
  color: var(--text-muted);
  margin-top: 0.5rem;
  line-height: 1.4;
}

.FAQEditorModal .modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--light-gray);
  background: var(--bg-gray);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

.FAQEditorModal .btn-primary:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(var(--primary-rgb), 0.3);
}

.FAQEditorModal .btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.FAQEditorModal .btn-secondary {
  background: var(--white);
  color: var(--text-dark);
  border-color: var(--light-gray);
}

.FAQEditorModal .btn-secondary:hover:not(:disabled) {
  background: var(--bg-gray);
  border-color: var(--text-muted);
}

.FAQEditorModal .spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .FAQEditorModal {
    width: 95%;
    max-height: 95vh;
  }

  .FAQEditorModal .modal-header {
    padding: 1.25rem 1.5rem;
  }

  .FAQEditorModal .modal-title {
    font-size: 1.25rem;
  }

  .FAQEditorModal .header-icon {
    font-size: 1.25rem;
  }

  .FAQEditorModal .modal-body {
    padding: 1.5rem;
  }

  .FAQEditorModal .form-row {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .FAQEditorModal .modal-footer {
    padding: 1.25rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .FAQEditorModal {
    width: 98%;
    max-height: 98vh;
  }

  .FAQEditorModal .modal-header {
    padding: 1rem 1.25rem;
  }

  .FAQEditorModal .modal-title {
    font-size: 1.125rem;
  }

  .FAQEditorModal .modal-body {
    padding: 1.25rem;
  }

  .FAQEditorModal .form-group {
    margin-bottom: 1.25rem;
  }

  .FAQEditorModal .form-input,
  .FAQEditorModal .form-textarea,
  .FAQEditorModal .form-select {
    padding: 0.75rem;
    font-size: 0.875rem;
  }

  .FAQEditorModal .modal-footer {
    padding: 1rem 1.25rem;
    flex-direction: column-reverse;
  }
}
