const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const jobScheduler = require('../jobs/scheduler');

const router = express.Router();

// Protected admin routes
router.use(protect);
router.use(authorize('admin'));

// @desc    Get job scheduler status
// @route   GET /api/jobs/status
// @access  Private/Admin
router.get('/status', (req, res) => {
    try {
        const status = jobScheduler.getStatus();
        res.status(200).json({
            success: true,
            data: status
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Manually run a specific job
// @route   POST /api/jobs/run/:jobName
// @access  Private/Admin
router.post('/run/:jobName', async (req, res) => {
    try {
        const { jobName } = req.params;

        const validJobs = ['orderCleanup', 'runnerUpNotification'];
        if (!validJobs.includes(jobName)) {
            return res.status(400).json({
                success: false,
                error: `Invalid job name. Valid jobs: ${validJobs.join(', ')}`
            });
        }

        const result = await jobScheduler.runJob(jobName);

        res.status(200).json({
            success: true,
            message: `Job '${jobName}' executed successfully`,
            data: result
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Start job scheduler
// @route   POST /api/jobs/start
// @access  Private/Admin
router.post('/start', (req, res) => {
    try {
        jobScheduler.start();
        res.status(200).json({
            success: true,
            message: 'Job scheduler started successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Stop job scheduler
// @route   POST /api/jobs/stop
// @access  Private/Admin
router.post('/stop', (req, res) => {
    try {
        jobScheduler.stop();
        res.status(200).json({
            success: true,
            message: 'Job scheduler stopped successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router; 