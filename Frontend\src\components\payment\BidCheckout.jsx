import React, { useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { loadStripe } from "@stripe/stripe-js";
import {
  Elements,
  CardElement,
  useStripe,
  useElements,
} from "@stripe/react-stripe-js";
import { FaCreditCard, FaLock, FaArrowLeft } from "react-icons/fa";
import LoadingSkeleton from "../common/LoadingSkeleton";
import api from "../../services/api";
import { API_BASE_URL, STORAGE_KEYS } from "../../utils/constants";
import "../../styles/BidCheckout.css";
import { usePlatformCommission } from '../../hooks/useSettings';

const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY);

const CheckoutForm = ({ bidData, onSuccess }) => {
  const stripe = useStripe();
  const elements = useElements();
  const dispatch = useDispatch();
  const { user } = useSelector((state) => state.auth);

  const [isProcessing, setIsProcessing] = useState(false);
  const [clientSecret, setClientSecret] = useState("");

  // Use platform commission hook
  const { platformCommission } = usePlatformCommission();

  useEffect(() => {
    createPaymentIntent();
  }, []);

  const createPaymentIntent = async () => {
    try {
      const response = await api.post("/payments/create-bid-payment-intent", {
        bidId: bidData.bidId || bidData._id,
        amount: bidData.amount,
      });

      if (response.data.success) {
        setClientSecret(response.data.clientSecret);
      } else {
        throw new Error(response.data.message || "Failed to create payment intent");
      }
    } catch (error) {
      console.error("Error creating payment intent:", error);
      toast.error("Failed to initialize payment. Please try again.");
    }
  };

  const handleSubmit = async (event) => {
    event.preventDefault();

    if (!stripe || !elements || !clientSecret) {
      return;
    }

    setIsProcessing(true);

    const cardElement = elements.getElement(CardElement);

    try {
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: `${user.firstName} ${user.lastName}`,
              email: user.email,
            },
          },
        }
      );

      if (error) {
        console.error("Payment failed:", error);
        toast.error(error.message || "Payment failed. Please try again.");
      } else if (paymentIntent.status === "succeeded") {
        // Payment successful, update bid status and create order
        await completeBidPurchase(paymentIntent.id);
        onSuccess(paymentIntent);
      }
    } catch (error) {
      console.error("Payment error:", error);
      toast.error("Payment failed. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const completeBidPurchase = async (paymentIntentId) => {
    try {
      const response = await api.post("/payments/complete-bid-purchase", {
        bidId: bidData.bidId || bidData._id,
        paymentIntentId,
      });

      if (!response.data.success) {
        throw new Error(response.data.message || "Failed to complete purchase");
      }

      return response.data;
    } catch (error) {
      console.error("Error completing purchase:", error);
      throw error;
    }
  };

  const cardElementOptions = {
    style: {
      base: {
        fontSize: "16px",
        color: "#424770",
        "::placeholder": {
          color: "#aab7c4",
        },
      },
      invalid: {
        color: "#9e2146",
      },
    },
  };

  return (
    <form onSubmit={handleSubmit} className="checkout-form">
      <div className="payment-section">
        <h3><FaCreditCard /> Payment Information</h3>

        <div className="card-element-container">
          <CardElement options={cardElementOptions} />
        </div>

        <div className="security-notice">
          <FaLock className="security-icon" />
          <span>Your payment information is secure and encrypted</span>
        </div>
      </div>

      <div className="order-summary">
        <h3>Order Summary</h3>
        <div className="summary-item">
          <span>Content:</span>
          <span>{bidData.content?.title}</span>
        </div>
        <div className="summary-item">
          <span>Content Price:</span>
          <span>${bidData.amount?.toFixed(2)}</span>
        </div>
        {/* <div className="summary-item platform-fee-info">
          <span>Platform Fee (included):</span>
          <span>${((bidData.amount || 0) * (platformCommission / 100)).toFixed(2)}</span>
        </div> */}
        <div className="summary-total">
          <span>Total:</span>
          <span>${(bidData.amount || 0).toFixed(2)}</span>
        </div>
        <div className="fee-explanation">
          <small>Platform fee is deducted from seller earnings</small>
        </div>
      </div>

      <button
        type="submit"
        disabled={!stripe || isProcessing || !clientSecret}
        className="pay-button"
      >
        {isProcessing ? "Processing..." : `Pay $${(bidData.amount || 0).toFixed(2)}`}
      </button>
    </form>
  );
};

const BidCheckout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const bidData = location.state;

  useEffect(() => {
    if (!bidData || bidData.type !== 'bid') {
      toast.error("Invalid checkout data");
      navigate('/buyer/account/bids');
    }
  }, [bidData, navigate]);

  const handlePaymentSuccess = (paymentIntent) => {
    toast.success("Payment successful! You can now download your content.");
    navigate('/buyer/account/downloads', {
      state: { paymentSuccess: true }
    });
  };

  const handleGoBack = () => {
    navigate('/buyer/account/bids');
  };

  if (!bidData) {
    return <LoadingSkeleton type="checkout" />;
  }

  return (
    <div className="bid-checkout">
      <div className="checkout-header">
        <button className="back-button" onClick={handleGoBack}>
          <FaArrowLeft /> Back to Bids
        </button>
        <h1>Complete Your Purchase</h1>
        <p>Pay for your winning bid to access the content</p>
      </div>

      <div className="checkout-container">
        <div className="checkout-content">
          <div className="content-preview">
            <img
              src={bidData.content?.thumbnailUrl || 'https://via.placeholder.com/300x200'}
              alt={bidData.content?.title}
              className="content-image"
            />
            <div className="content-details">
              <h2>{bidData.content?.title}</h2>
              <p>By {bidData.content?.seller?.firstName} {bidData.content?.seller?.lastName}</p>
              <p className="content-type">{bidData.content?.contentType}</p>
            </div>
          </div>

          <Elements stripe={stripePromise}>
            <CheckoutForm
              bidData={bidData}
              onSuccess={handlePaymentSuccess}
            />
          </Elements>
        </div>
      </div>
    </div>
  );
};

export default BidCheckout;
