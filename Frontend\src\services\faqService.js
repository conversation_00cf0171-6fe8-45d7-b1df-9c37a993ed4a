import api from './api';

// FAQ endpoints
const FAQ_ENDPOINTS = {
  PUBLIC: '/faqs',
  CATEGORIES: '/faqs/categories',
  SEARCH: '/faqs/search',
  ADMIN: '/admin/faqs',
  ADMIN_BULK_DELETE: '/admin/faqs/bulk-delete',
  ADMIN_REORDER: '/admin/faqs/reorder',
  ADMIN_STATUS: (id) => `/admin/faqs/${id}/status`
};

// Public FAQ Services
export const faqService = {
  // Get all active FAQs (public)
  getActiveFAQs: async (params = {}) => {
    try {
      const response = await api.get(FAQ_ENDPOINTS.PUBLIC, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching active FAQs:', error);
      throw error;
    }
  },

  // Get FAQ categories (public)
  getFAQCategories: async () => {
    try {
      const response = await api.get(FAQ_ENDPOINTS.CATEGORIES);
      return response.data;
    } catch (error) {
      console.error('Error fetching FAQ categories:', error);
      throw error;
    }
  },

  // Search FAQs (public)
  searchFAQs: async (searchTerm, params = {}) => {
    try {
      const response = await api.get(FAQ_ENDPOINTS.SEARCH, {
        params: { q: searchTerm, ...params }
      });
      return response.data;
    } catch (error) {
      console.error('Error searching FAQs:', error);
      throw error;
    }
  }
};

// Admin FAQ Services
export const adminFAQService = {
  // Get all FAQs with admin features
  getAllFAQs: async (params = {}) => {
    try {
      const response = await api.get(FAQ_ENDPOINTS.ADMIN, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching FAQs (admin):', error);
      throw error;
    }
  },

  // Get FAQ by ID
  getFAQById: async (id) => {
    try {
      const response = await api.get(`${FAQ_ENDPOINTS.ADMIN}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching FAQ by ID:', error);
      throw error;
    }
  },

  // Create new FAQ
  createFAQ: async (faqData) => {
    try {
      const response = await api.post(FAQ_ENDPOINTS.ADMIN, faqData);
      return response.data;
    } catch (error) {
      console.error('Error creating FAQ:', error);
      throw error;
    }
  },

  // Update FAQ
  updateFAQ: async (id, faqData) => {
    try {
      const response = await api.put(`${FAQ_ENDPOINTS.ADMIN}/${id}`, faqData);
      return response.data;
    } catch (error) {
      console.error('Error updating FAQ:', error);
      throw error;
    }
  },

  // Delete FAQ
  deleteFAQ: async (id) => {
    try {
      const response = await api.delete(`${FAQ_ENDPOINTS.ADMIN}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting FAQ:', error);
      throw error;
    }
  },

  // Bulk delete FAQs
  bulkDeleteFAQs: async (faqIds) => {
    try {
      const response = await api.post(FAQ_ENDPOINTS.ADMIN_BULK_DELETE, { faqIds });
      return response.data;
    } catch (error) {
      console.error('Error bulk deleting FAQs:', error);
      throw error;
    }
  },

  // Update FAQ status
  updateFAQStatus: async (id, status) => {
    try {
      const response = await api.patch(FAQ_ENDPOINTS.ADMIN_STATUS(id), { status });
      return response.data;
    } catch (error) {
      console.error('Error updating FAQ status:', error);
      throw error;
    }
  },

  // Reorder FAQs
  reorderFAQs: async (faqOrders) => {
    try {
      const response = await api.put(FAQ_ENDPOINTS.ADMIN_REORDER, { faqOrders });
      return response.data;
    } catch (error) {
      console.error('Error reordering FAQs:', error);
      throw error;
    }
  }
};

// Default export for backward compatibility
export default faqService;
