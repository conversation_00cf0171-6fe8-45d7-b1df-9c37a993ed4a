import React, { useEffect, useState } from "react";
import { useSelector, useDispatch } from "react-redux";
import { useLocation } from "react-router-dom";
import {
  selectActiveTab,
  setActiveTab,
  selectIsSidebarOpen,
  setSidebarOpen,
} from "../../redux/slices/adminDashboardSlice";
import AdminSidebar from "./AdminSidebar";
import AdminNavbar from "./AdminNavbar";
import "../../styles/AdminLayout.css";

// Icons for breadcrumbs
import { MdDashboard, MdPeople, MdVideoLibrary, MdRateReview } from "react-icons/md";
import { FaChartBar, FaFileAlt, FaCog, FaGavel, FaHandshake, FaClipboardList, FaShoppingCart, FaRegChartBar, FaQuestionCircle } from "react-icons/fa";

const AdminLayout = ({ children }) => {
  const dispatch = useDispatch();
  const location = useLocation();
  const activeTab = useSelector(selectActiveTab);
  const isSidebarOpen = useSelector(selectIsSidebarOpen);
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  // Map routes to tabs
  const routeToTabMap = {
    "/admin/dashboard": "dashboard",
    "/admin/users": "users",
    "/admin/content": "content",
    "/admin/bids": "bids",
    "/admin/offers": "offers",
    "/admin/orders": "orders",
    "/admin/requests": "requests",
    "/admin/reviews": "reviews",
    "/admin/reports": "reports",
    "/admin/cms": "cms",
    "/admin/faqs": "faqs",
    "/admin/settings": "settings",
  };

  // Map tabs to headers with icons
  const tabToHeaderMap = {
    dashboard: {
      title: "Dashboard Overview",
      icon: <MdDashboard className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard"],
    },
    users: {
      title: "User Management",
      icon: <MdPeople className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "User Management"],
    },
    content: {
      title: "Content Management",
      icon: <MdVideoLibrary className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Content Management"],
    },
    bids: {
      title: "Bid Management",
      icon: <FaGavel className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Bid Management"],
    },
    offers: {
      title: "Offer Management",
      icon: <FaHandshake className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Offer Management"],
    },
    orders: {
      title: "Order Management",
      icon: <FaShoppingCart className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Order Management"],
    },
    requests: {
      title: "Request Management",
      icon: <FaClipboardList className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Request Management"],
    },
    reviews: {
      title: "Review Management",
      icon: <MdRateReview className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Review Management"],
    },
    reports: {
      title: "Reports & Analytics",
      icon: <FaRegChartBar className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Reports & Analytics"],
    },
    cms: {
      title: "CMS Pages",
      icon: <FaFileAlt className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "CMS Pages"],
    },
    faqs: {
      title: "FAQ Management",
      icon: <FaQuestionCircle className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "FAQ Management"],
    },
    settings: {
      title: "Settings",
      icon: <FaCog className="AdminLayout__title-icon" />,
      breadcrumb: ["Dashboard", "Settings"],
    },
  };

  // Update active tab based on current route
  useEffect(() => {
    const currentTab = routeToTabMap[location.pathname];
    if (currentTab && currentTab !== activeTab) {
      dispatch(setActiveTab(currentTab));
    }
  }, [location.pathname, activeTab, dispatch]);

  // Get current header info
  const currentHeader = tabToHeaderMap[activeTab] || tabToHeaderMap.dashboard;

  // Handle sidebar toggle
  const handleToggleSidebar = () => {
    if (window.innerWidth <= 768) {
      // Mobile and small tablet (≤768px): toggle sidebar visibility
      dispatch(setSidebarOpen(!isSidebarOpen));
    } else {
      // Desktop and large screens (>768px): toggle sidebar collapse
      // Note: This function should not be called on larger screens as the toggle button is hidden
      setSidebarCollapsed(!sidebarCollapsed);
    }
  };

  // Handle mobile sidebar close
  const handleMobileSidebarClose = () => {
    if (window.innerWidth <= 768) {
      dispatch(setSidebarOpen(false));
    }
  };

  // Handle window resize
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth > 768) {
        // On larger screens (>768px): ensure sidebar is always visible and close mobile overlay
        dispatch(setSidebarOpen(false));
        // Reset collapsed state on very large screens
        if (window.innerWidth > 1024) {
          setSidebarCollapsed(false);
        }
      } else {
        // On mobile/small tablets (≤768px): ensure sidebar is closed by default and reset collapse
        setSidebarCollapsed(false);
        // Optionally close mobile sidebar when resizing to mobile
        dispatch(setSidebarOpen(false));
      }
    };

    // Run on initial load
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [dispatch]);

  return (
    <div className="AdminLayout">
      {/* Top Navbar */}
      <AdminNavbar
        onToggleSidebar={handleToggleSidebar}
        sidebarCollapsed={sidebarCollapsed}
      />

      <div className="AdminLayout__container">
        {/* Sidebar */}
        <div className={`AdminLayout__sidebar ${sidebarCollapsed ? 'collapsed' : ''} ${isSidebarOpen ? 'mobile-open' : ''}`}>
          <AdminSidebar />
        </div>

        {/* Mobile Sidebar Overlay */}
        {isSidebarOpen && (
          <div
            className="AdminLayout__overlay"
            onClick={handleMobileSidebarClose}
          />
        )}

        {/* Main Content */}
        <div className={`AdminLayout__main ${sidebarCollapsed ? 'sidebar-collapsed' : ''}`}>
          {/* Breadcrumb Navigation */}
          <div className="AdminLayout__breadcrumb">
            <nav className="breadcrumb-nav">
              {currentHeader.breadcrumb.map((item, index) => (
                <span key={index} className="breadcrumb-item">
                  {item}
                  {index < currentHeader.breadcrumb.length - 1 && (
                    <span className="breadcrumb-separator">/</span>
                  )}
                </span>
              ))}
            </nav>
          </div>

          {/* Page Content */}
          <div className="AdminLayout__content">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLayout;
