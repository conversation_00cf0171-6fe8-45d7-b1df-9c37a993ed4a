// Phone validation utilities for restricted country codes

// Only allow India (+91) and USA (+1)
export const ALLOWED_COUNTRY_CODES = ['+91', '+1'];

/**
 * Validate phone number based on country code
 * @param {string} countryCode - Country code (+91 or +1)
 * @param {string} phone - Phone number without country code
 * @returns {Object} - Validation result with isValid and error message
 */
export const validatePhoneNumber = (countryCode, phone) => {
  // Check if country code is allowed
  if (!ALLOWED_COUNTRY_CODES.includes(countryCode)) {
    return {
      isValid: false,
      error: 'Only India (+91) and USA (+1) phone numbers are allowed'
    };
  }

  // Remove any non-digit characters
  const cleanPhone = phone.replace(/\D/g, '');

  // Validate based on country code
  if (countryCode === '+91') {
    // India: 10 digits
    if (!cleanPhone) {
      return {
        isValid: false,
        error: 'Phone number is required'
      };
    }
    if (cleanPhone.length !== 10) {
      return {
        isValid: false,
        error: 'Indian phone number must be 10 digits'
      };
    }
    // Indian mobile numbers start with 6, 7, 8, or 9
    if (!/^[6-9]/.test(cleanPhone)) {
      return {
        isValid: false,
        error: 'Invalid Indian phone number format'
      };
    }
  } else if (countryCode === '+1') {
    // USA: 10 digits
    if (!cleanPhone) {
      return {
        isValid: false,
        error: 'Phone number is required'
      };
    }
    if (cleanPhone.length !== 10) {
      return {
        isValid: false,
        error: 'US phone number must be 10 digits'
      };
    }
    // US phone numbers: area code cannot start with 0 or 1
    if (/^[01]/.test(cleanPhone)) {
      return {
        isValid: false,
        error: 'Invalid US phone number format'
      };
    }
  }

  return {
    isValid: true,
    error: null
  };
};

/**
 * Format phone number for display
 * @param {string} countryCode - Country code
 * @param {string} phone - Phone number
 * @returns {string} - Formatted phone number
 */
export const formatPhoneNumber = (countryCode, phone) => {
  const cleanPhone = phone.replace(/\D/g, '');
  
  if (countryCode === '+91') {
    // Format: +91 12345 67890
    if (cleanPhone.length === 10) {
      return `${countryCode} ${cleanPhone.slice(0, 5)} ${cleanPhone.slice(5)}`;
    }
  } else if (countryCode === '+1') {
    // Format: +****************
    if (cleanPhone.length === 10) {
      return `${countryCode} (${cleanPhone.slice(0, 3)}) ${cleanPhone.slice(3, 6)}-${cleanPhone.slice(6)}`;
    }
  }
  
  return `${countryCode} ${cleanPhone}`;
};

/**
 * Get full phone number with country code
 * @param {string} countryCode - Country code
 * @param {string} phone - Phone number
 * @returns {string} - Full phone number
 */
export const getFullPhoneNumber = (countryCode, phone) => {
  const cleanPhone = phone.replace(/\D/g, '');
  return `${countryCode}${cleanPhone}`;
};

/**
 * Parse full phone number into country code and phone
 * @param {string} fullPhone - Full phone number with country code
 * @returns {Object} - Object with countryCode and phone
 */
export const parsePhoneNumber = (fullPhone) => {
  if (!fullPhone) {
    return { countryCode: '+91', phone: '' };
  }

  const cleanPhone = fullPhone.replace(/\D/g, '');
  
  // Check for Indian number (+91)
  if (fullPhone.startsWith('+91') || fullPhone.startsWith('91')) {
    const phone = cleanPhone.startsWith('91') ? cleanPhone.slice(2) : cleanPhone;
    return { countryCode: '+91', phone };
  }
  
  // Check for US number (+1)
  if (fullPhone.startsWith('+1') || fullPhone.startsWith('1')) {
    const phone = cleanPhone.startsWith('1') ? cleanPhone.slice(1) : cleanPhone;
    return { countryCode: '+1', phone };
  }
  
  // Default to India if no country code detected
  return { countryCode: '+91', phone: cleanPhone };
};
