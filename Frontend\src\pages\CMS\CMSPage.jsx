import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';
import '../../styles/CMSPage.css';

const CMSPage = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const [page, setPage] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchPage = async () => {
      try {
        setLoading(true);
        const response = await fetch(`${import.meta.env.VITE_API_URL || 'http://localhost:5000/api'}/cms/${slug}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Page not found');
          } else {
            setError('Failed to load page');
          }
          return;
        }

        const data = await response.json();
        if (data.success) {
          setPage(data.data);
        } else {
          setError('Failed to load page');
        }
      } catch (err) {
        console.error('Error fetching CMS page:', err);
        setError('Failed to load page');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchPage();
    }
  }, [slug]);

  if (loading) {
    return (
      <div className="CMSPage">
        <div className="CMSPage__container max-containermax-container">
          <div className="CMSPage__loading">
            <div className="loading-spinner"></div>
            <p>Loading page...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="CMSPage">
        <div className="CMSPage__container max-container">
          <div className="CMSPage__error">
            <h1>Page Not Found</h1>
            <p>{error}</p>
            <button 
              className="btn btn-primary"
              onClick={() => navigate('/')}
            >
              Go Home
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!page) {
    return null;
  }

  return (
    <>
      <Helmet>
        <title>{page.metaTitle || page.title} - XOSportsHub</title>
        <meta name="description" content={page.metaDescription || `${page.title} - XOSportsHub`} />
        <meta name="keywords" content={page.metaKeywords || ''} />
        <meta property="og:title" content={page.metaTitle || page.title} />
        <meta property="og:description" content={page.metaDescription || `${page.title} - XOSportsHub`} />
        {page.featuredImage && (
          <meta property="og:image" content={page.featuredImage} />
        )}
        <meta property="og:type" content="article" />
      </Helmet>

      <div className="CMSPage">
        <div className="CMSPage__container max-container">
          <article className="CMSPage__article">
            {/* Featured Image */}
            {page.featuredImage && (
              <div className="CMSPage__featured-image">
                <img 
                  src={page.featuredImage} 
                  alt={page.title}
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                />
              </div>
            )}

            {/* Page Header */}
            <header className="CMSPage__header">
              <h1 className="CMSPage__title">{page.title}</h1>
              {page.metaDescription && (
                <p className="CMSPage__description">{page.metaDescription}</p>
              )}
              <div className="CMSPage__meta">
                <time className="CMSPage__date">
                  {new Date(page.updatedAt || page.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </time>
              </div>
            </header>

            {/* Page Content */}
            <div className="CMSPage__content">
              <div 
                className="CMSPage__html-content"
                dangerouslySetInnerHTML={{ __html: page.content }}
              />
            </div>
          </article>
        </div>
      </div>
    </>
  );
};

export default CMSPage;
