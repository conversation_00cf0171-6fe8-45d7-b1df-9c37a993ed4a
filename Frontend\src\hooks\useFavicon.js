import { useEffect } from 'react';
import { usePublicSettings } from './useSettings';
import { IMAGE_BASE_URL } from '../utils/constants';

/**
 * Custom hook to dynamically update the favicon based on settings
 */
export const useFavicon = () => {
  const { publicSettings } = usePublicSettings();

  useEffect(() => {
    const updateFavicon = () => {
      // Get the favicon URL from settings
      const faviconUrl = publicSettings?.siteFavicon;

      if (faviconUrl) {
        // Remove existing favicon links
        const existingFavicons = document.querySelectorAll('link[rel*="icon"]');
        existingFavicons.forEach(link => link.remove());

        // Create the full URL for the favicon
        const fullFaviconUrl = faviconUrl.startsWith('http')
          ? faviconUrl
          : `${IMAGE_BASE_URL}${faviconUrl}`;

        // Create new favicon link
        const link = document.createElement('link');
        link.rel = 'icon';
        link.type = 'image/x-icon';
        link.href = fullFaviconUrl;

        // Add to document head
        document.head.appendChild(link);

        // Also create a shortcut icon for better browser compatibility
        const shortcutLink = document.createElement('link');
        shortcutLink.rel = 'shortcut icon';
        shortcutLink.type = 'image/x-icon';
        shortcutLink.href = fullFaviconUrl;
        document.head.appendChild(shortcutLink);

        // Add apple-touch-icon for mobile devices
        const appleLink = document.createElement('link');
        appleLink.rel = 'apple-touch-icon';
        appleLink.href = fullFaviconUrl;
        document.head.appendChild(appleLink);
      }
    };

    // Update favicon when settings change
    if (publicSettings) {
      updateFavicon();
    }
  }, [publicSettings?.siteFavicon]);

  return null; // This hook doesn't return anything, just manages side effects
};

export default useFavicon;
