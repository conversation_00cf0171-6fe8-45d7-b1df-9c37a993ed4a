import React from 'react';
import { <PERSON>a<PERSON>ye, FaEdit, FaTrash } from 'react-icons/fa';
import '../../styles/AdminTableActions.css';

/**
 * Reusable Admin Table Actions Component
 * Provides consistent View, Edit, Delete action buttons for admin tables
 * 
 * @param {Object} props
 * @param {Object} props.item - The data item for the row
 * @param {Function} props.onView - Handler for view action
 * @param {Function} props.onEdit - Handler for edit action  
 * @param {Function} props.onDelete - Handler for delete action
 * @param {Object} props.permissions - Object defining which actions are available
 * @param {boolean} props.permissions.view - Show view button (default: true)
 * @param {boolean} props.permissions.edit - Show edit button (default: true)
 * @param {boolean} props.permissions.delete - Show delete button (default: true)
 * @param {Object} props.tooltips - Custom tooltip text for actions
 * @param {string} props.tooltips.view - Custom view tooltip
 * @param {string} props.tooltips.edit - Custom edit tooltip
 * @param {string} props.tooltips.delete - Custom delete tooltip
 * @param {string} props.className - Additional CSS classes
 * @param {boolean} props.disabled - Disable all actions
 */
const AdminTableActions = ({
  item,
  onView,
  onEdit,
  onDelete,
  permissions = {
    view: true,
    edit: true,
    delete: true
  },
  tooltips = {
    view: 'View Details',
    edit: 'Edit',
    delete: 'Delete'
  },
  className = '',
  disabled = false
}) => {
  const handleAction = (action, handler) => {
    if (disabled || !handler) return;
    handler(item);
  };

  return (
    <div className={`admin-table-actions ${className}`}>
      {permissions.view && onView && (
        <button
          className="admin-action-btn admin-action-view"
          title={tooltips.view}
          onClick={() => handleAction('view', onView)}
          disabled={disabled}
          aria-label={`${tooltips.view} for ${item?.name || item?.title || 'item'}`}
        >
          <FaEye />
        </button>
      )}

      {permissions.delete && onDelete && (
        <button
          className="admin-action-btn admin-action-delete"
          title={tooltips.delete}
          onClick={() => handleAction('delete', onDelete)}
          disabled={disabled}
          aria-label={`${tooltips.delete} ${item?.name || item?.title || 'item'}`}
        >
          <FaTrash />
        </button>
      )}
    </div>
  );
};

export default AdminTableActions;
