const nodemailer = require('nodemailer');

const sendEmail = async (options) => {
  console.log("📧 DEBUG: sendEmail function called with options:");
  console.log("  - to:", options.to);
  console.log("  - email:", options.email);
  console.log("  - subject:", options.subject);
  console.log("  - message length:", options.message ? options.message.length : 'undefined');
  console.log("  - html length:", options.html ? options.html.length : 'undefined');

  // Determine recipient email - support both 'to' and 'email' fields
  const recipientEmail = options.to || options.email;

  if (!recipientEmail) {
    console.error("❌ ERROR: No recipient email found in options");
    console.error("   Full options object:", JSON.stringify(options, null, 2));
    throw new Error("No recipient email specified");
  }

  console.log("✅ DEBUG: Using recipient email:", recipientEmail);

  // Create a transporter
  // const transporter = nodemailer.createTransport({
  //   service: process.env.EMAIL_SERVICE,
  //   auth: {
  //     user: process.env.EMAIL_USERNAME,
  //     pass: process.env.EMAIL_PASSWORD
  //   }
  // });

  const transporter = nodemailer.createTransport({
    host: process.env.EMAIL_HOST,
    port: parseInt(process.env.EMAIL_PORT, 10),
    secure: process.env.EMAIL_SECURE === 'true', // true for port 465, false for 587
    auth: {
      user: process.env.EMAIL_USERNAME,
      pass: process.env.EMAIL_PASSWORD
    }
  });

  // Define email options
  const mailOptions = {
    from: `${process.env.FROM_NAME} <${process.env.EMAIL_FROM}>`,
    to: recipientEmail,
    subject: options.subject,
    text: options.message,
    html: options.html
  };

  console.log("📬 DEBUG: Final mail options:");
  console.log("  - from:", mailOptions.from);
  console.log("  - to:", mailOptions.to);
  console.log("  - subject:", mailOptions.subject);

  // Send email
  const info = await transporter.sendMail(mailOptions);

  console.log(`✅ SUCCESS: Message sent with ID: ${info.messageId}`);
};

module.exports = sendEmail;
