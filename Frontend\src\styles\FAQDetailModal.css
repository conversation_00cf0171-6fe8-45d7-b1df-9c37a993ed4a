/* FAQ Detail Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
}

.FAQDetailModal {
  background: var(--white);
  border-radius: 16px;
  width: 90%;
  max-width: 700px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 1001;
}

.FAQDetailModal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  border-bottom: 1px solid var(--light-gray);
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  color: var(--white);
}

.FAQDetailModal .header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.FAQDetailModal .header-icon {
  font-size: 1.5rem;
  opacity: 0.9;
}

.FAQDetailModal .modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.FAQDetailModal .close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: var(--white);
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1.125rem;
}

.FAQDetailModal .close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.FAQDetailModal .modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 2rem;
}

.FAQDetailModal .detail-row {
  display: flex;
  gap: 2rem;
  margin-bottom: 1.5rem;
}

.FAQDetailModal .detail-group {
  flex: 1;
}

.FAQDetailModal .detail-group.full-width {
  flex: none;
  width: 100%;
}

.FAQDetailModal .detail-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.75rem;
}

.FAQDetailModal .label-icon {
  font-size: 0.875rem;
  color: var(--primary-color);
}

.FAQDetailModal .status-display {
  display: flex;
  align-items: center;
}

.FAQDetailModal .status-badge {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 25px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.FAQDetailModal .status-badge.active {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.FAQDetailModal .status-badge.inactive {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.FAQDetailModal .order-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: linear-gradient(
    135deg,
    var(--primary-color) 0%,
    var(--secondary-color) 100%
  );
  color: var(--white);
  border-radius: 50%;
  font-size: 1.125rem;
  font-weight: 700;
}

.FAQDetailModal .category-tag {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(var(--primary-rgb), 0.1);
  color: var(--primary-color);
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: capitalize;
}

.FAQDetailModal .question-display {
  background: var(--bg-gray);
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  padding: 1.25rem;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.6;
}

.FAQDetailModal .answer-display {
  background: var(--bg-gray);
  border: 1px solid var(--light-gray);
  border-radius: 8px;
  padding: 1.25rem;
  font-size: 1rem;
  color: var(--text-dark);
  line-height: 1.7;
  white-space: pre-wrap;
}

.FAQDetailModal .metadata-section {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--light-gray);
}

.FAQDetailModal .metadata-title {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-dark);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.FAQDetailModal .metadata-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
}

.FAQDetailModal .metadata-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.FAQDetailModal .metadata-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.8125rem;
  font-weight: 600;
  color: var(--text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.FAQDetailModal .metadata-value {
  font-size: 0.9375rem;
  color: var(--text-dark);
  font-weight: 500;
}

.FAQDetailModal .modal-footer {
  padding: 1.5rem 2rem;
  border-top: 1px solid var(--light-gray);
  background: var(--bg-gray);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .FAQDetailModal {
    width: 95%;
    max-height: 95vh;
  }

  .FAQDetailModal .modal-header {
    padding: 1.25rem 1.5rem;
  }

  .FAQDetailModal .modal-title {
    font-size: 1.25rem;
  }

  .FAQDetailModal .header-icon {
    font-size: 1.25rem;
  }

  .FAQDetailModal .modal-body {
    padding: 1.5rem;
  }

  .FAQDetailModal .detail-row {
    flex-direction: column;
    gap: 1rem;
  }

  .FAQDetailModal .metadata-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .FAQDetailModal .modal-footer {
    padding: 1.25rem 1.5rem;
  }
}

@media (max-width: 480px) {
  .FAQDetailModal {
    width: 98%;
    max-height: 98vh;
  }

  .FAQDetailModal .modal-header {
    padding: 1rem 1.25rem;
  }

  .FAQDetailModal .modal-title {
    font-size: 1.125rem;
  }

  .FAQDetailModal .modal-body {
    padding: 1.25rem;
  }

  .FAQDetailModal .question-display,
  .FAQDetailModal .answer-display {
    padding: 1rem;
    font-size: 0.9375rem;
  }

  .FAQDetailModal .metadata-section {
    margin-top: 1.5rem;
    padding-top: 1.5rem;
  }

  .FAQDetailModal .modal-footer {
    padding: 1rem 1.25rem;
    flex-direction: column;
  }

  .FAQDetailModal .btn-secondary {
    order: 2;
  }
}
