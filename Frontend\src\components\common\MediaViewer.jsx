import React, { useState } from "react";
import { FaTimes, FaDownload, FaExpand, FaCompress } from "react-icons/fa";
import { MdPlayArrow, MdPause } from "react-icons/md";
import SimplePDFViewer from "./SimplePDFViewer";
import DocumentViewer from "./DocumentViewer";
import "../../styles/MediaViewer.css";

const MediaViewer = ({
  isOpen,
  onClose,
  fileUrl,
  fileName,
  fileType,
  title
}) => {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);

  if (!isOpen) return null;

  // Determine file type from URL or provided type
  const getFileType = () => {
    if (fileType) return fileType.toLowerCase();
    if (!fileUrl) return 'unknown';

    const extension = fileUrl.split('.').pop().toLowerCase();
    const videoExtensions = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm', 'mkv'];
    const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];
    const audioExtensions = ['mp3', 'wav', 'ogg', 'aac', 'flac'];
    const documentFormats = [
      'pdf' // Only PDF documents are supported
    ];

    if (videoExtensions.includes(extension)) return 'video';
    if (imageExtensions.includes(extension)) return 'image';
    if (audioExtensions.includes(extension)) return 'audio';
    if (documentFormats.includes(extension)) return 'document';

    return 'unknown';
  };

  const mediaType = getFileType();

  const handleDownload = () => {
    // Download functionality disabled for security purposes
    console.warn('Download functionality has been disabled for security purposes');
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleOverlayClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const renderMedia = () => {
    switch (mediaType) {
      case 'video':
        return (
          <video
            className="media-viewer__video"
            controls
            autoPlay={false}
            controlsList="nodownload nofullscreen noremoteplayback"
            disablePictureInPicture
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          >
            <source src={fileUrl} type="video/mp4" />
            Your browser does not support the video tag.
          </video>
        );

      case 'audio':
        return (
          <div className="media-viewer__audio-container">
            <div className="media-viewer__audio-info">
              <h3>{fileName || title || 'Audio File'}</h3>
              <p>Audio Content</p>
            </div>
            <audio
              className="media-viewer__audio"
              controls
              onPlay={() => setIsPlaying(true)}
              onPause={() => setIsPlaying(false)}
            >
              <source src={fileUrl} type="audio/mpeg" />
              Your browser does not support the audio tag.
            </audio>
          </div>
        );

      case 'image':
        return (
          <img
            className="media-viewer__image"
            src={fileUrl}
            alt={fileName || title || 'Image'}
          />
        );

      case 'document':
        return (
          <DocumentViewer
            fileUrl={fileUrl}
            fileName={fileName || title || 'Document'}
            title={title || 'Document'}
            className="media-viewer__document"
            height="100%"
            showDownload={false}
            onDownload={handleDownload}
          />
        );

      default:
        return (
          <div className="media-viewer__unsupported">
            <div className="media-viewer__unsupported-content">
              <h3>File Preview Not Available</h3>
              <p>This file type cannot be previewed in the browser.</p>
              <p>File: {fileName || 'Unknown file'}</p>
              <button
                className="media-viewer__download-btn"
                onClick={handleDownload}
              >
                <FaDownload /> Download File
              </button>
            </div>
          </div>
        );
    }
  };

  return (
    <div className="media-viewer-overlay" onClick={handleOverlayClick}>
      <div className={`media-viewer ${isFullscreen ? 'media-viewer--fullscreen' : ''}`}>
        {/* Header */}
        <div className="media-viewer__header">
          <div className="media-viewer__title">
            <h3>{title || fileName || 'Media Preview'}</h3>
            <span className="media-viewer__type">{mediaType.toUpperCase()}</span>
          </div>
          <div className="media-viewer__controls">
            <button
              className="media-viewer__control-btn"
              onClick={handleDownload}
              title="Download"
            >
              <FaDownload />
            </button>
            <button
              className="media-viewer__control-btn"
              onClick={toggleFullscreen}
              title={isFullscreen ? "Exit Fullscreen" : "Fullscreen"}
            >
              {isFullscreen ? <FaCompress /> : <FaExpand />}
            </button>
            <button
              className="media-viewer__control-btn media-viewer__close-btn"
              onClick={onClose}
              title="Close"
            >
              <FaTimes />
            </button>
          </div>
        </div>

        {/* Media Content */}
        <div className="media-viewer__content">
          {renderMedia()}
        </div>

        {/* Footer */}
        <div className="media-viewer__footer">
          <div className="media-viewer__info">
            <span>File: {fileName || 'Unknown'}</span>
            {fileUrl && (
              <span>
                • <a href={fileUrl} target="_blank" rel="noopener noreferrer">
                  Open in new tab
                </a>
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MediaViewer;
