const ErrorResponse = require('../utils/errorResponse');
const Setting = require('../models/Setting');

/**
 * Middleware to check launch status and maintenance mode
 * Restricts access for non-admin users during restricted periods
 */
exports.checkLaunchStatus = async (req, res, next) => {
  try {
    // Skip check for certain routes that should always be accessible
    const allowedPaths = [
      '/api/settings/public',
      '/api/auth',
      '/api/health'
    ];
    
    // Check if current path should be allowed
    const isAllowedPath = allowedPaths.some(path => req.path.startsWith(path));
    if (isAllowedPath) {
      return next();
    }
    
    // Get restriction status
    const restrictionStatus = await Setting.checkRestrictionStatus();
    
    // If site is not restricted, allow access
    if (!restrictionStatus.restricted) {
      return next();
    }
    
    // Check if user is authenticated and is admin
    if (req.user && req.user.role === 'admin') {
      // Admins can always access the site
      return next();
    }
    
    // For restricted access, return appropriate error
    const errorMessage = restrictionStatus.reason === 'maintenance' 
      ? 'Site is currently under maintenance. Please try again later.'
      : 'Site is not yet launched. Please check back later.';
    
    return next(new ErrorResponse(errorMessage, 503, {
      restrictionStatus,
      reason: restrictionStatus.reason,
      ...(restrictionStatus.launchDate && { launchDate: restrictionStatus.launchDate })
    }));
    
  } catch (error) {
    console.error('Launch status check error:', error);
    // If there's an error checking launch status, allow access to prevent site lockout
    return next();
  }
};

/**
 * Middleware specifically for API routes that need launch status info
 * Adds launch status to response headers for frontend consumption
 */
exports.addLaunchStatusHeaders = async (req, res, next) => {
  try {
    const restrictionStatus = await Setting.checkRestrictionStatus();
    
    // Add headers for frontend to consume
    res.set({
      'X-Site-Restricted': restrictionStatus.restricted.toString(),
      'X-Restriction-Reason': restrictionStatus.reason || 'none',
      ...(restrictionStatus.launchDate && {
        'X-Launch-Date': restrictionStatus.launchDate.toISOString()
      })
    });
    
    next();
  } catch (error) {
    console.error('Launch status header error:', error);
    next();
  }
};

/**
 * Express error handler for launch status errors
 * Formats restriction errors appropriately
 */
exports.handleLaunchStatusError = (err, req, res, next) => {
  // Only handle launch status related errors
  if (err.statusCode === 503 && err.data && err.data.restrictionStatus) {
    return res.status(503).json({
      success: false,
      error: err.message,
      restrictionStatus: err.data.restrictionStatus,
      reason: err.data.reason,
      ...(err.data.launchDate && { launchDate: err.data.launchDate })
    });
  }
  
  // Pass other errors to the next error handler
  next(err);
};
