import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import {
  selectLaunchDateTime,
  selectSiteName,
  selectSiteLogo
} from '../redux/slices/settingsSlice';
import { fetchPublicSettings } from '../redux/slices/settingsSlice';
import { useLaunchCountdown } from '../hooks/useCountdown';
import { IMAGE_BASE_URL } from '../utils/constants';
import authService from '../services/authService';
import '../styles/LaunchCountdown.css';

// Icons
import {
  FaClock,
  FaRocket,
  FaUserShield,
  FaStar,
  FaGem,
  FaFire
} from 'react-icons/fa';

const LaunchCountdownPage = () => {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const launchDateTime = useSelector(selectLaunchDateTime);
  const siteName = useSelector(selectSiteName);
  const siteLogo = useSelector(selectSiteLogo);

  // Use the countdown hook for launch functionality
  const countdown = useLaunchCountdown(launchDateTime);
  const { timeLeft, isComplete: isLaunched, isAlreadyLaunched } = countdown;

  // Check if user is already logged in as admin
  const userData = authService.getStoredUser();
  const isAdmin = userData?.role === 'admin';

  // Handle admin login redirect
  const handleAdminLogin = () => {
    navigate('/auth?admin=true');
  };

  // Auto-redirect when countdown completes or if already launched
  useEffect(() => {
    if (isLaunched || isAlreadyLaunched) {
      // Refresh settings to clear restriction status
      dispatch(fetchPublicSettings());
      
      // Add a small delay for better UX
      const redirectTimer = setTimeout(() => {
        window.location.href = '/'; // Use window.location for full page refresh
      }, 2000);
      
      return () => clearTimeout(redirectTimer);
    }
  }, [isLaunched, isAlreadyLaunched, dispatch]);



  // Get site logo URL
  const getSiteLogoUrl = () => {
    if (!siteLogo) return null;
    if (siteLogo.startsWith('http')) return siteLogo;
    return `${IMAGE_BASE_URL || 'http://localhost:5000'}${siteLogo}`;
  };

  const logoUrl = getSiteLogoUrl();

  // If launched or already launched, show launch message
  if (isLaunched || isAlreadyLaunched) {
    return (
      <div className="launch-countdown">
        <div className="launch-countdown__container">
          <div className="launch-countdown__content">
            <div className="launch-countdown__header">
              {logoUrl && (
                <img 
                  src={logoUrl} 
                  alt={siteName} 
                  className="launch-countdown__logo"
                />
              )}
              <div className="launch-countdown__icon-group">
                <FaRocket className="launch-countdown__icon launch-countdown__icon--primary" />
                <FaStar className="launch-countdown__icon launch-countdown__icon--secondary" />
                <FaFire className="launch-countdown__icon launch-countdown__icon--tertiary" />
              </div>
              <h1 className="launch-countdown__title">
                🚀 We're Live!
              </h1>
            </div>
            <div className="launch-countdown__launch-message">
              <p className="launch-countdown__message">
                {siteName} is now officially launched!
              </p>
              <p className="launch-countdown__submessage">
                Get ready for the ultimate sports content marketplace experience.
              </p>
              <div className="launch-countdown__redirect-notice">
                <FaClock className="launch-countdown__redirect-icon" />
                <span>Redirecting you to the site in a moment...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="launch-countdown">
      <div className="launch-countdown__container">
        <div className="launch-countdown__content">
          <div className="launch-countdown__header">
            {logoUrl && (
              <img 
                src={logoUrl} 
                alt={siteName} 
                className="launch-countdown__logo"
              />
            )}
            <div className="launch-countdown__icon-group">
              <FaClock className="launch-countdown__icon launch-countdown__icon--primary" />
              <FaRocket className="launch-countdown__icon launch-countdown__icon--secondary" />
              <FaGem className="launch-countdown__icon launch-countdown__icon--tertiary" />
            </div>
            <h1 className="launch-countdown__title">
              Coming Soon
            </h1>
          </div>

          <div className="launch-countdown__timer-section">
            <p className="launch-countdown__message">
              Something amazing is coming to {siteName}!
            </p>
            <p className="launch-countdown__submessage">
              Get ready for the ultimate sports content marketplace experience.
            </p>

            {launchDateTime && (
              <div className="launch-countdown__timer">
                <div className="timer-unit">
                  <span className="timer-number">{timeLeft.days}</span>
                  <span className="timer-label">Days</span>
                </div>
                <div className="timer-separator">:</div>
                <div className="timer-unit">
                  <span className="timer-number">{timeLeft.hours}</span>
                  <span className="timer-label">Hours</span>
                </div>
                <div className="timer-separator">:</div>
                <div className="timer-unit">
                  <span className="timer-number">{timeLeft.minutes}</span>
                  <span className="timer-label">Minutes</span>
                </div>
                <div className="timer-separator">:</div>
                <div className="timer-unit">
                  <span className="timer-number">{timeLeft.seconds}</span>
                  <span className="timer-label">Seconds</span>
                </div>
              </div>
            )}
          </div>



          {!isAdmin && (
            <div className="launch-countdown__admin-login">
              <button
                onClick={handleAdminLogin}
                className="admin-login-btn"
                title="Admin Login"
                type="button"
              >
                <FaUserShield />
                <span>Admin Login</span>
              </button>
            </div>
          )}
        </div>

        <div className="launch-countdown__background">
          {/* <div className="background-animation"></div> */}
        </div>
      </div>
    </div>
  );
};

export default LaunchCountdownPage;
