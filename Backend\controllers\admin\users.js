const { validationResult } = require('express-validator');
const ErrorResponse = require('../../utils/errorResponse');
const User = require('../../models/User');
const Order = require('../../models/Order');
const Content = require('../../models/Content');
const Payment = require('../../models/Payment');
const Bid = require('../../models/Bid');
const Offer = require('../../models/Offer');
const CustomRequest = require('../../models/CustomRequest');
const Review = require('../../models/Review');
const Notification = require('../../models/Notification');
const Wishlist = require('../../models/Wishlist');
const Card = require('../../models/Card');
const Conversation = require('../../models/Conversation');
const Message = require('../../models/Message');

// @desc    Get all users with filtering, sorting, and pagination
// @route   GET /api/admin/users
// @access  Private/Admin
exports.getAllUsers = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      search = '',
      role = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc',
      dateFrom = '',
      dateTo = ''
    } = req.query;

    // Build query
    let query = {};

    // Search filter
    if (search) {
      query.$or = [
        { firstName: { $regex: search, $options: 'i' } },
        { lastName: { $regex: search, $options: 'i' } },
        { email: { $regex: search, $options: 'i' } }
      ];
    }

    // Role filter - use activeRole for non-admin users, role for admin users
    if (role) {
      if (role === 'admin') {
        query.role = 'admin';
      } else {
        query.activeRole = role;
      }
    }

    // Status filter
    if (status) {
      // Convert string status to numeric if needed
      if (typeof status === 'string') {
        switch (status.toLowerCase()) {
          case 'active':
            query.status = 1;
            break;
          case 'inactive':
            query.status = 0;
            break;
          case 'deleted':
            query.status = -1;
            break;
          default:
            query.status = parseInt(status) || status;
        }
      } else {
        query.status = status;
      }
    }

    // Exclude soft-deleted users by default (unless specifically filtering for deleted status)
    if (!status || (status !== 'deleted' && status !== '-1')) {
      query.status = { $ne: -1 };
    }

    // Date range filter
    if (dateFrom || dateTo) {
      query.createdAt = {};
      if (dateFrom) query.createdAt.$gte = new Date(dateFrom);
      if (dateTo) query.createdAt.$lte = new Date(dateTo);
    }

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const sortOptions = {};
    sortOptions[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Execute query
    const users = await User.find(query)
      .select('-password')
      .sort(sortOptions)
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await User.countDocuments(query);

    // Add additional user statistics
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        const userObj = user.toObject();

        if (user.getEffectiveRole() === 'buyer') {
          const orders = await Order.find({ buyer: user._id });
          userObj.totalPurchases = orders.length;
          userObj.totalSpent = orders.reduce((sum, order) => sum + order.amount, 0);
        } else if (user.getEffectiveRole() === 'seller') {
          const content = await Content.find({ seller: user._id });
          const payments = await Payment.find({ seller: user._id, status: 'Completed' });
          userObj.totalContent = content.length;
          userObj.totalEarnings = payments.reduce((sum, payment) => sum + payment.sellerEarnings, 0);
        }

        return userObj;
      })
    );

    res.status(200).json({
      success: true,
      data: {
        users: usersWithStats,
        pagination: {
          current: parseInt(page),
          pages: Math.ceil(total / parseInt(limit)),
          total,
          limit: parseInt(limit)
        }
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user by ID with detailed information
// @route   GET /api/admin/users/:id
// @access  Private/Admin
exports.getUserById = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id).select('-password');

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    // Get user statistics based on role
    let userStats = {};

    if (user.getEffectiveRole() === 'buyer') {
      const orders = await Order.find({ buyer: user._id })
        .populate('content', 'title sport contentType')
        .populate('seller', 'firstName lastName')
        .sort('-createdAt');

      const payments = await Payment.find({
        order: { $in: orders.map(order => order._id) }
      });

      userStats = {
        totalOrders: orders.length,
        completedOrders: orders.filter(order => order.status === 'Completed').length,
        totalSpent: payments.reduce((sum, payment) => sum + payment.amount, 0),
        recentOrders: orders.slice(0, 5),
        favoriteCategories: await getFavoriteCategories(orders)
      };
    } else if (user.getEffectiveRole() === 'seller') {
      const content = await Content.find({ seller: user._id }).sort('-createdAt');
      const payments = await Payment.find({ seller: user._id, status: 'Completed' });
      const orders = await Order.find({ seller: user._id });

      userStats = {
        totalContent: content.length,
        publishedContent: content.filter(c => c.status === 'Published').length,
        pendingContent: content.filter(c => c.status === 'Under Review').length,
        totalSales: orders.length,
        totalEarnings: payments.reduce((sum, payment) => sum + payment.sellerEarnings, 0),
        recentContent: content.slice(0, 5),
        topSellingContent: await getTopSellingContent(user._id)
      };
    }

    res.status(200).json({
      success: true,
      data: {
        user: user.toObject(),
        stats: userStats
      }
    });
  } catch (err) {
    next(err);
  }
};

// Helper function to get favorite categories for buyers
const getFavoriteCategories = async (orders) => {
  const categoryCount = {};

  orders.forEach(order => {
    if (order.content && order.content.contentType) {
      categoryCount[order.content.contentType] = (categoryCount[order.content.contentType] || 0) + 1;
    }
  });

  return Object.entries(categoryCount)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 3)
    .map(([category, count]) => ({ category, count }));
};

// Helper function to get top selling content for sellers
const getTopSellingContent = async (sellerId) => {
  return await Order.aggregate([
    {
      $lookup: {
        from: 'contents',
        localField: 'content',
        foreignField: '_id',
        as: 'contentInfo'
      }
    },
    { $unwind: '$contentInfo' },
    { $match: { 'contentInfo.seller': sellerId } },
    {
      $group: {
        _id: '$content',
        title: { $first: '$contentInfo.title' },
        sales: { $sum: 1 },
        revenue: { $sum: '$amount' }
      }
    },
    { $sort: { sales: -1 } },
    { $limit: 5 }
  ]);
};

// @desc    Create new user
// @route   POST /api/admin/users
// @access  Private/Admin
exports.createUser = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { firstName, lastName, email, mobile, role, password = 'TempPassword123!' } = req.body;

    // Check if user already exists (excluding soft-deleted users)
    const existingUser = await User.findOne({ email, status: { $ne: -1 } });
    if (existingUser) {
      return next(new ErrorResponse('User with this email already exists', 400));
    }

    // Create user
    const user = await User.create({
      firstName,
      lastName,
      email,
      mobile,
      role,
      password,
      isVerified: true, // Admin created users are auto-verified
      status: 1 // Active
    });

    // Remove password from response
    user.password = undefined;

    res.status(201).json({
      success: true,
      data: user
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update user
// @route   PUT /api/admin/users/:id
// @access  Private/Admin
exports.updateUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    // Update user fields
    const allowedFields = ['firstName', 'lastName', 'email', 'mobile', 'phone', 'role', 'status', 'isVerified'];
    const updateData = {};

    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        // Handle phone field mapping to mobile
        if (field === 'phone') {
          updateData['mobile'] = req.body[field];
        } else if (field === 'status') {
          // Convert string status to numeric
          if (typeof req.body[field] === 'string') {
            switch (req.body[field].toLowerCase()) {
              case 'active':
                updateData[field] = 1;
                break;
              case 'inactive':
                updateData[field] = 0;
                break;
              case 'deleted':
                updateData[field] = -1;
                break;
              default:
                updateData[field] = req.body[field];
            }
          } else {
            updateData[field] = req.body[field];
          }
        } else {
          updateData[field] = req.body[field];
        }
      }
    });

    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      updateData,
      { new: true, runValidators: true }
    ).select('-password');

    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete user
// @route   DELETE /api/admin/users/:id
// @access  Private/Admin
exports.deleteUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    // Perform hard delete - remove user and all related data
    const userId = req.params.id;

    // Delete all related data in parallel for better performance
    await Promise.all([
      // Delete user's content
      Content.deleteMany({ seller: userId }),

      // Delete orders where user is buyer or seller
      Order.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

      // Delete bids made by user
      Bid.deleteMany({ bidder: userId }),

      // Delete offers where user is buyer or seller
      Offer.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

      // Delete custom requests where user is buyer or seller
      CustomRequest.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

      // Delete payments where user is buyer or seller
      Payment.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

      // Delete reviews by user
      Review.deleteMany({ user: userId }),

      // Delete notifications for user
      Notification.deleteMany({ user: userId }),

      // Delete user's wishlist items
      Wishlist.deleteMany({ user: userId }),

      // Delete user's saved cards
      Card.deleteMany({ user: userId }),

      // Delete conversations where user is participant
      Conversation.deleteMany({ participants: userId }),

      // Delete messages sent by user and remove user from readBy arrays
      Message.deleteMany({ sender: userId }),
      Message.updateMany(
        { 'readBy.user': userId },
        { $pull: { readBy: { user: userId } } }
      )
    ]);

    // Finally, delete the user
    await User.findByIdAndDelete(userId);

    res.status(200).json({
      success: true,
      message: 'User and all related data deleted successfully'
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk update users
// @route   POST /api/admin/users/bulk-update
// @access  Private/Admin
exports.bulkUpdateUsers = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { userIds, action, data = {} } = req.body;

    let updateData = {};

    switch (action) {
      case 'activate':
        updateData = { status: 1 }; // Active
        break;
      case 'deactivate':
        updateData = { status: 0 }; // Inactive
        break;
      case 'verify':
        updateData = { isVerified: true };
        break;
      case 'unverify':
        updateData = { isVerified: false };
        break;
      case 'update_role':
        if (!data.role) {
          return next(new ErrorResponse('Role is required for role update', 400));
        }
        updateData = { role: data.role };
        break;
      default:
        return next(new ErrorResponse('Invalid bulk action', 400));
    }

    const result = await User.updateMany(
      { _id: { $in: userIds } },
      updateData
    );

    res.status(200).json({
      success: true,
      message: `${result.modifiedCount} users updated successfully`,
      data: {
        matched: result.matchedCount,
        modified: result.modifiedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Toggle user status
// @route   PUT /api/admin/users/:id/toggle-status
// @access  Private/Admin
exports.toggleUserStatus = async (req, res, next) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    const newStatus = user.status === 1 ? 0 : 1; // Toggle between active (1) and inactive (0)

    const updatedUser = await User.findByIdAndUpdate(
      req.params.id,
      { status: newStatus },
      { new: true }
    ).select('-password');

    res.status(200).json({
      success: true,
      data: updatedUser
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user statistics
// @route   GET /api/admin/users/stats
// @access  Private/Admin
exports.getUserStats = async (req, res, next) => {
  try {
    const totalUsers = await User.countDocuments({ status: { $ne: -1 } }); // Exclude soft-deleted
    const activeUsers = await User.countDocuments({ status: 1 });
    const verifiedUsers = await User.countDocuments({ isVerified: true, status: { $ne: -1 } });

    const usersByRole = await User.aggregate([
      { $match: { status: { $ne: -1 } } }, // Exclude soft-deleted
      {
        $group: {
          _id: '$role',
          count: { $sum: 1 }
        }
      }
    ]);

    const usersByStatus = await User.aggregate([
      { $match: { status: { $ne: -1 } } }, // Exclude soft-deleted
      {
        $group: {
          _id: '$status',
          count: { $sum: 1 }
        }
      }
    ]);

    // New users in last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const newUsers = await User.countDocuments({
      createdAt: { $gte: thirtyDaysAgo },
      status: { $ne: -1 } // Exclude soft-deleted
    });

    res.status(200).json({
      success: true,
      data: {
        total: totalUsers,
        active: activeUsers,
        verified: verifiedUsers,
        newInLast30Days: newUsers,
        byRole: usersByRole,
        byStatus: usersByStatus
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Export users data
// @route   GET /api/admin/users/export
// @access  Private/Admin
exports.exportUsers = async (req, res, next) => {
  try {
    const { format = 'csv', role = '', status = '' } = req.query;

    let query = {};
    if (role) {
      if (role === 'admin') {
        query.role = 'admin';
      } else {
        query.activeRole = role;
      }
    }
    if (status) {
      // Convert string status to numeric if needed
      if (typeof status === 'string') {
        switch (status.toLowerCase()) {
          case 'active':
            query.status = 1;
            break;
          case 'inactive':
            query.status = 0;
            break;
          case 'deleted':
            query.status = -1;
            break;
          default:
            query.status = parseInt(status) || status;
        }
      } else {
        query.status = status;
      }
    }

    const users = await User.find(query)
      .select('-password')
      .sort('-createdAt');

    // Add user statistics
    const usersWithStats = await Promise.all(
      users.map(async (user) => {
        const userObj = user.toObject();

        if (user.getEffectiveRole() === 'buyer') {
          const orders = await Order.find({ buyer: user._id });
          userObj.totalPurchases = orders.length;
          userObj.totalSpent = orders.reduce((sum, order) => sum + order.amount, 0);
        } else if (user.getEffectiveRole() === 'seller') {
          const content = await Content.find({ seller: user._id });
          const payments = await Payment.find({ seller: user._id, status: 'Completed' });
          userObj.totalContent = content.length;
          userObj.totalEarnings = payments.reduce((sum, payment) => sum + payment.sellerEarnings, 0);
        }

        return userObj;
      })
    );

    if (format === 'json') {
      res.status(200).json({
        success: true,
        data: usersWithStats
      });
    } else {
      // For CSV format, you would implement CSV generation here
      // For now, returning JSON with a note
      res.status(200).json({
        success: true,
        message: 'CSV export functionality to be implemented',
        data: usersWithStats
      });
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Bulk delete users
// @route   POST /api/admin/users/bulk-delete
// @access  Private/Admin
exports.bulkDeleteUsers = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return next(new ErrorResponse('Validation failed', 400, errors.array()));
    }

    const { userIds } = req.body;

    let deletedCount = 0;

    for (const userId of userIds) {
      const user = await User.findById(userId);
      if (!user) continue;

      // Perform hard delete - remove user and all related data
      await Promise.all([
        // Delete user's content
        Content.deleteMany({ seller: userId }),

        // Delete orders where user is buyer or seller
        Order.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

        // Delete bids made by user
        Bid.deleteMany({ bidder: userId }),

        // Delete offers where user is buyer or seller
        Offer.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

        // Delete custom requests where user is buyer or seller
        CustomRequest.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

        // Delete payments where user is buyer or seller
        Payment.deleteMany({ $or: [{ buyer: userId }, { seller: userId }] }),

        // Delete reviews by user
        Review.deleteMany({ user: userId }),

        // Delete notifications for user
        Notification.deleteMany({ user: userId }),

        // Delete user's wishlist items
        Wishlist.deleteMany({ user: userId }),

        // Delete user's saved cards
        Card.deleteMany({ user: userId }),

        // Delete conversations where user is participant
        Conversation.deleteMany({ participants: userId }),

        // Delete messages sent by user and remove user from readBy arrays
        Message.deleteMany({ sender: userId }),
        Message.updateMany(
          { 'readBy.user': userId },
          { $pull: { readBy: { user: userId } } }
        )
      ]);

      // Finally, delete the user
      await User.findByIdAndDelete(userId);
      deletedCount++;
    }

    res.status(200).json({
      success: true,
      message: `${deletedCount} users and all related data deleted successfully`,
      data: {
        deleted: deletedCount,
        total: deletedCount
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user activity history
// @route   GET /api/admin/users/:id/activity
// @access  Private/Admin
exports.getUserActivityHistory = async (req, res, next) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const skip = (parseInt(page) - 1) * parseInt(limit);

    const user = await User.findById(req.params.id);
    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    let activities = [];

    if (user.getEffectiveRole() === 'buyer') {
      const orders = await Order.find({ buyer: user._id })
        .populate('content', 'title')
        .populate('seller', 'firstName lastName')
        .sort('-createdAt')
        .skip(skip)
        .limit(parseInt(limit));

      activities = orders.map(order => ({
        id: order._id,
        type: 'order',
        description: `Purchased "${order.content?.title}"`,
        amount: order.amount,
        status: order.status,
        timestamp: order.createdAt,
        relatedUser: `${order.seller?.firstName} ${order.seller?.lastName}`
      }));
    } else if (user.getEffectiveRole() === 'seller') {
      const content = await Content.find({ seller: user._id })
        .sort('-createdAt')
        .skip(skip)
        .limit(parseInt(limit));

      const orders = await Order.find({ seller: user._id })
        .populate('content', 'title')
        .populate('buyer', 'firstName lastName')
        .sort('-createdAt')
        .skip(skip)
        .limit(parseInt(limit));

      const contentActivities = content.map(item => ({
        id: item._id,
        type: 'content_upload',
        description: `Uploaded "${item.title}"`,
        status: item.status,
        timestamp: item.createdAt
      }));

      const orderActivities = orders.map(order => ({
        id: order._id,
        type: 'sale',
        description: `Sold "${order.content?.title}"`,
        amount: order.amount,
        status: order.status,
        timestamp: order.createdAt,
        relatedUser: `${order.buyer?.firstName} ${order.buyer?.lastName}`
      }));

      activities = [...contentActivities, ...orderActivities]
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
        .slice(0, parseInt(limit));
    }

    res.status(200).json({
      success: true,
      data: activities
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Verify user
// @route   PUT /api/admin/users/:id/verify
// @access  Private/Admin
exports.verifyUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.params.id,
      { isVerified: true },
      { new: true }
    ).select('-password');

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Suspend user
// @route   PUT /api/admin/users/:id/suspend
// @access  Private/Admin
exports.suspendUser = async (req, res, next) => {
  try {
    const { reason } = req.body;

    const user = await User.findByIdAndUpdate(
      req.params.id,
      {
        status: 0, // Inactive (suspended)
        suspensionReason: reason,
        suspendedAt: new Date()
      },
      { new: true }
    ).select('-password');

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Unsuspend user
// @route   PUT /api/admin/users/:id/unsuspend
// @access  Private/Admin
exports.unsuspendUser = async (req, res, next) => {
  try {
    const user = await User.findByIdAndUpdate(
      req.params.id,
      {
        status: 1, // Active
        $unset: { suspensionReason: 1, suspendedAt: 1 }
      },
      { new: true }
    ).select('-password');

    if (!user) {
      return next(new ErrorResponse('User not found', 404));
    }

    res.status(200).json({
      success: true,
      data: user
    });
  } catch (err) {
    next(err);
  }
};
