import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import "../../styles/BuyerDashboard.css";
import StrategyCard from "../../components/common/StrategyCard";
import { useSelector, useDispatch } from "react-redux";
import {
  selectAllStrategies,
  selectLoading,
  selectErrors,
  selectFiltersBySection,
  selectSearchTermBySection,
  selectPaginationBySection,
  selectCategories,
  fetchAllStrategies,
  fetchContentCategories,
  updateFilters,
  updateSearchTerm,
  updatePagination,
  clearError
} from "../../redux/slices/buyerDashboardSlice";
import LoadingSkeleton, { StrategiesGridSkeleton } from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { IoMdSearch } from "react-icons/io";
import { IoClose } from "react-icons/io5";
import { FaFilter, FaSync } from "react-icons/fa";
import {
  IMAGE_BASE_URL,
  getProxyThumbnailUrl,
  getProxyUrlWithAuth,
  getPublicThumbnailUrl,
} from "../../utils/constants";
import Pagination from "../../components/common/Pagination";

// Inside the BuyerDashboard component
const BuyerDashboard = () => {
  const dispatch = useDispatch();
  const location = useLocation();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  // Redux selectors
  const strategies = useSelector(selectAllStrategies);
  const loading = useSelector(selectLoading);
  const errors = useSelector(selectErrors);
  const filters = useSelector(state => selectFiltersBySection(state, 'strategies'));
  const searchTerm = useSelector(state => selectSearchTermBySection(state, 'strategies'));
  const pagination = useSelector(state => selectPaginationBySection(state, 'strategies'));
  const categories = useSelector(selectCategories);

  // Initialize sport from URL parameters first, then fallback to filters
  const getInitialSport = () => {
    const searchParams = new URLSearchParams(location.search);
    const sportParam = searchParams.get('sport');
    return sportParam || filters.sport;
  };

  // Local state for UI
  const [currentPage, setCurrentPage] = useState(pagination.page);
  const [selectedSport, setSelectedSport] = useState(getInitialSport());
  const [sortOption, setSortOption] = useState(filters.sortBy);

  // Dynamic filter options from API
  const saleTypeOptions = [
    { id: "Fixed", label: "Buy Now Content" },
    { id: "Auction", label: "Bid Now Content" },
  ];

  // Get dynamic options from categories
  const availableSports = categories.sports || [];
  const availableContentTypes = categories.contentTypes || [];
  const availableDifficultyLevels = categories.difficultyLevels || [];

  // Price range state
  const [priceRange, setPriceRange] = useState(filters.priceRange);
  const [isSliderActive, setIsSliderActive] = useState(false);
  const [isDragging, setIsDragging] = useState(false);

  // Filter checkboxes state for different filter types
  const [checkedItems, setCheckedItems] = useState({
    // Sale type filters
    saleType: filters.saleType.reduce((acc, type) => ({ ...acc, [type]: true }), {}),
    // Content type filters
    contentType: filters.contentType.reduce((acc, type) => ({ ...acc, [type]: true }), {}),
    // Difficulty filters
    difficulty: filters.difficulty.reduce((acc, level) => ({ ...acc, [level]: true }), {}),
  });

  // Handle URL parameters for sport filtering
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const sportParam = searchParams.get('sport');

    if (sportParam) {
      // Always update if we have a sport parameter from URL
      setSelectedSport(sportParam);
      dispatch(updateFilters({
        section: 'strategies',
        filters: { sport: sportParam }
      }));
    }
  }, [location.search, dispatch]);

  // Fetch categories on component mount
  useEffect(() => {
    dispatch(fetchContentCategories());
  }, [dispatch]);

  // Update price range when categories change
  useEffect(() => {
    if (categories.priceRange?.maxPrice && categories.priceRange.maxPrice > 0) {
      const newMaxPrice = categories.priceRange.maxPrice;
      // Only update if the current range is still using the default values
      if (priceRange[1] === 5000 || priceRange[1] === 1000) {
        setPriceRange([0, newMaxPrice]);
      }
    }
  }, [categories.priceRange?.maxPrice]);

  // Fetch strategies on component mount and when filters change (but not during dragging)
  useEffect(() => {
    // Skip API call if user is currently dragging
    if (isDragging) return;

    // Get filtered arrays for each filter type
    const saleTypeFilters = Object.keys(checkedItems.saleType).filter(key => checkedItems.saleType[key]);
    const contentTypeFilters = Object.keys(checkedItems.contentType).filter(key => checkedItems.contentType[key]);
    const difficultyFilters = Object.keys(checkedItems.difficulty).filter(key => checkedItems.difficulty[key]);

    // Get dynamic max price
    const dynamicMaxPrice = categories.priceRange?.maxPrice || 5000;

    const params = {
      page: currentPage,
      limit: 9, // Always use backend's fixed limit
      sport: selectedSport === "All" ? undefined : selectedSport,
      saleType: saleTypeFilters.length > 0 ? saleTypeFilters : undefined,
      contentType: contentTypeFilters.length > 0 ? contentTypeFilters : undefined,
      difficulty: difficultyFilters.length > 0 ? difficultyFilters : undefined,
      priceMin: priceRange[0] > 0 ? priceRange[0] : undefined,
      priceMax: priceRange[1] < dynamicMaxPrice ? priceRange[1] : undefined,
      sortBy: sortOption,
      search: searchTerm || undefined,
    };

    // Remove empty arrays and undefined values
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || (Array.isArray(params[key]) && params[key].length === 0)) {
        delete params[key];
      }
    });

    dispatch(fetchAllStrategies(params));
  }, [dispatch, currentPage, selectedSport, checkedItems, priceRange, sortOption, searchTerm, isDragging, categories.priceRange?.maxPrice]);

  // Handle retry
  const handleRetry = () => {
    dispatch(clearError('strategies'));

    // Get filtered arrays for each filter type
    const saleTypeFilters = Object.keys(checkedItems.saleType).filter(key => checkedItems.saleType[key]);
    const contentTypeFilters = Object.keys(checkedItems.contentType).filter(key => checkedItems.contentType[key]);
    const difficultyFilters = Object.keys(checkedItems.difficulty).filter(key => checkedItems.difficulty[key]);

    // Get dynamic max price
    const dynamicMaxPrice = categories.priceRange?.maxPrice || 5000;

    const params = {
      page: currentPage,
      limit: 9, // Always use backend's fixed limit
      sport: selectedSport === "All" ? undefined : selectedSport,
      saleType: saleTypeFilters.length > 0 ? saleTypeFilters : undefined,
      contentType: contentTypeFilters.length > 0 ? contentTypeFilters : undefined,
      difficulty: difficultyFilters.length > 0 ? difficultyFilters : undefined,
      priceMin: priceRange[0] > 0 ? priceRange[0] : undefined,
      priceMax: priceRange[1] < dynamicMaxPrice ? priceRange[1] : undefined,
      sortBy: sortOption,
      search: searchTerm || undefined,
    };

    // Remove empty arrays and undefined values
    Object.keys(params).forEach(key => {
      if (params[key] === undefined || (Array.isArray(params[key]) && params[key].length === 0)) {
        delete params[key];
      }
    });

    dispatch(fetchAllStrategies(params));
  };

  // Handle drawer open/close
  const toggleDrawer = () => {
    setIsDrawerOpen(!isDrawerOpen);
  };

  // Close drawer when clicking overlay
  const handleOverlayClick = (e) => {
    if (e.target.classList.contains("filter-overlay")) {
      setIsDrawerOpen(false);
    }
  };

  // Close drawer on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === "Escape") {
        setIsDrawerOpen(false);
      }
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, []);

  // Handle scroll lock when drawer is open on mobile
  useEffect(() => {
    if (isDrawerOpen) {
      // Add scroll lock class to body with scoped name
      document.body.classList.add('buyer-dashboard-filter-open');
      // Store current scroll position
      const scrollY = window.scrollY;
      document.body.style.top = `-${scrollY}px`;
    } else {
      // Remove scroll lock class
      document.body.classList.remove('buyer-dashboard-filter-open');
      // Restore scroll position
      const scrollY = document.body.style.top;
      document.body.style.top = '';
      if (scrollY) {
        window.scrollTo(0, parseInt(scrollY || '0') * -1);
      }
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('buyer-dashboard-filter-open');
      document.body.style.top = '';
    };
  }, [isDrawerOpen]);

  // Handle dynamic viewport height for mobile browsers
  useEffect(() => {
    const setVH = () => {
      const vh = window.innerHeight * 0.01;
      // Set the CSS variable on the buyer-dashboard element specifically
      const buyerDashboard = document.querySelector('.buyer-dashboard');
      if (buyerDashboard) {
        buyerDashboard.style.setProperty('--vh', `${vh}px`);
      }
    };

    // Set initial value
    setVH();

    // Update on resize and orientation change
    window.addEventListener('resize', setVH);
    window.addEventListener('orientationchange', setVH);

    // Cleanup
    return () => {
      window.removeEventListener('resize', setVH);
      window.removeEventListener('orientationchange', setVH);
    };
  }, []);

  // Handle checkbox change for different filter types
  const handleCheckboxChange = (filterType, itemName, checked) => {
    setCheckedItems(prev => ({
      ...prev,
      [filterType]: {
        ...prev[filterType],
        [itemName]: checked,
      }
    }));
  };

  // Handle search input change
  const handleSearchChange = (e) => {
    const newSearchTerm = e.target.value;
    dispatch(updateSearchTerm({ section: 'strategies', searchTerm: newSearchTerm }));
  };

  // Handle sort change
  const handleSortChange = (e) => {
    const newSortOption = e.target.value;
    setSortOption(newSortOption);
    dispatch(updateFilters({
      section: 'strategies',
      filters: { ...filters, sortBy: newSortOption }
    }));
  };

  // Handle pagination
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    dispatch(updatePagination({
      section: 'strategies',
      pagination: { ...pagination, page: pageNumber }
    }));
  };

  // Clear all filters
  const clearFilters = () => {
    const clearedItems = {
      saleType: {},
      contentType: {},
      difficulty: {},
    };
    // Use dynamic max price for clearing
    const dynamicMaxPrice = categories.priceRange?.maxPrice || 5000;
    const clearedPriceRange = [0, dynamicMaxPrice];

    setCheckedItems(clearedItems);
    setPriceRange(clearedPriceRange);
    setSelectedSport("All");

    dispatch(updateFilters({
      section: 'strategies',
      filters: {
        sport: "All",
        saleType: [],
        contentType: [],
        difficulty: [],
        priceRange: clearedPriceRange,
        sortBy: "newest"
      }
    }));
  };

  return (
    <div className="buyer-dashboard">
      <div className=" max-container">
        {/* Filter Overlay */}
        {isDrawerOpen && (
          <div className="filter-overlay" onClick={handleOverlayClick} />
        )}

        {/* Filter Section */}
        <div className={`filter-section ${isDrawerOpen ? "drawer-open" : ""}`}>
          <button className="close-drawer-btn" onClick={toggleDrawer}>
            <IoClose />
          </button>
          <div className="filter-header">
            <h3>Filter By</h3>
            <button className="clear-all" onClick={clearFilters}>
              Clear All
            </button>
          </div>

          {/* Sport Filter */}
          <div className="filter-group">
            <h4>Sport</h4>
            <div className="sport-select-wrapper">
              <select
                className="sport-select"
                value={selectedSport}
                onChange={(e) => {
                  const newSport = e.target.value;
                  setSelectedSport(newSport);
                  dispatch(updateFilters({
                    section: 'strategies',
                    filters: { sport: newSport }
                  }));
                }}
              >
                <option value="All">All Sports</option>
                {availableSports.map((sport) => (
                  <option key={sport} value={sport}>
                    {sport}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Sale Type Filters */}
          <div className="filter-group">
            <h4>Sale Type</h4>
            <div className="checkbox-group">
              {saleTypeOptions.map((option) => (
                <div className="checkbox-item" key={option.id}>
                  <input
                    type="checkbox"
                    id={`saleType-${option.id}`}
                    checked={checkedItems.saleType[option.id] || false}
                    onChange={(e) => handleCheckboxChange('saleType', option.id, e.target.checked)}
                  />
                  <label htmlFor={`saleType-${option.id}`}>{option.label}</label>
                </div>
              ))}
            </div>
          </div>
          {/* Content Types Filters */}
          <div className="filter-group">
            <h4>Content Type</h4>
            <div className="checkbox-group">
              {availableContentTypes.map((contentType) => (
                <div className="checkbox-item" key={contentType}>
                  <input
                    type="checkbox"
                    id={`contentType-${contentType}`}
                    checked={checkedItems.contentType[contentType] || false}
                    onChange={(e) => handleCheckboxChange('contentType', contentType, e.target.checked)}
                  />
                  <label htmlFor={`contentType-${contentType}`}>{contentType}</label>
                </div>
              ))}
            </div>
          </div>
          {/* Difficulty Filters */}
          <div className="filter-group">
            <h4>Difficulty Level</h4>
            <div className="checkbox-group">
              {availableDifficultyLevels.map((difficulty) => (
                <div className="checkbox-item" key={difficulty}>
                  <input
                    type="checkbox"
                    id={`difficulty-${difficulty}`}
                    checked={checkedItems.difficulty[difficulty] || false}
                    onChange={(e) => handleCheckboxChange('difficulty', difficulty, e.target.checked)}
                  />
                  <label htmlFor={`difficulty-${difficulty}`}>{difficulty}</label>
                </div>
              ))}
            </div>
          </div>

          {/* Price Filter */}
          <div className="filter-group">
            <h4>Price</h4>
            <div className="price-range">
              <div
                className={`price-slider-container ${isSliderActive ? 'active' : ''}`}
                style={{
                  "--min-value": priceRange[0],
                  "--max-value": priceRange[1],
                  "--max-price": categories.priceRange?.maxPrice || 5000,
                }}
              >
                <input
                  type="range"
                  min="0"
                  max={categories.priceRange?.maxPrice || 5000}
                  step="1"
                  value={priceRange[0]}
                  onChange={(e) => {
                    const newMin = parseInt(e.target.value);
                    if (newMin < priceRange[1] - 1) {
                      setPriceRange([newMin, priceRange[1]]);
                    }
                  }}
                  onMouseDown={() => {
                    setIsSliderActive(true);
                    setIsDragging(true);
                  }}
                  onMouseUp={() => {
                    setIsSliderActive(false);
                    setIsDragging(false);
                  }}
                  onTouchStart={() => {
                    setIsSliderActive(true);
                    setIsDragging(true);
                  }}
                  onTouchEnd={() => {
                    setIsSliderActive(false);
                    setIsDragging(false);
                  }}
                  className="price-slider min-slider"
                />
                <input
                  type="range"
                  min="0"
                  max={categories.priceRange?.maxPrice || 5000}
                  step="1"
                  value={priceRange[1]}
                  onChange={(e) => {
                    const newMax = parseInt(e.target.value);
                    if (newMax > priceRange[0] + 1) {
                      setPriceRange([priceRange[0], newMax]);
                    }
                  }}
                  onMouseDown={() => {
                    setIsSliderActive(true);
                    setIsDragging(true);
                  }}
                  onMouseUp={() => {
                    setIsSliderActive(false);
                    setIsDragging(false);
                  }}
                  onTouchStart={() => {
                    setIsSliderActive(true);
                    setIsDragging(true);
                  }}
                  onTouchEnd={() => {
                    setIsSliderActive(false);
                    setIsDragging(false);
                  }}
                  className="price-slider max-slider"
                />
              </div>
              <div className="price-labels">
                <span>${priceRange[0].toLocaleString()}</span>
                <span>${priceRange[1].toLocaleString()}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Content Section */}
        <div className="content-section">
          <div className="content-header">
            <div className="content-title-section">
              <h2 className="content-title">
                Sports Content
                <span>
                  {loading.strategies ?
                    "(Loading...)" :
                    `(${pagination.total || strategies.length} Contents Found)`
                  }
                </span>
              </h2>
              {errors.strategies && (
                <button
                  className="retry-btn"
                  onClick={handleRetry}
                  title="Retry loading content"
                >
                  <FaSync />
                </button>
              )}
            </div>

            <div className="search-sort">
              <div className="search-container">
                <input
                  type="text"
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={handleSearchChange}
                  className="search-input"
                />
                <button className="search-button">
                  <IoMdSearch />
                </button>
              </div>

              <div className="sort-container">
                <select
                  id="sort"
                  className="sort-select"
                  value={sortOption}
                  onChange={handleSortChange}
                >
                  <option value="newest">Newest first</option>
                  <option value="oldest">Oldest first</option>
                  <option value="price_desc">Price high to low</option>
                  <option value="price_asc">Price low to high</option>
                  <option value="rating">Highest rated</option>
                  <option value="rating_asc">Lowest rated</option>
                </select>
              </div>
            </div>
          </div>
          <button className="filter-toggle-btn" onClick={toggleDrawer}>
            Filter <FaFilter />
          </button>

          {/* Error Display */}
          {errors.strategies ? (
            <ErrorDisplay
              error={errors.strategies}
              onRetry={handleRetry}
              title="Failed to load content"
              className="strategies-error"
            />
          ) : loading.strategies ? (
            /* Loading Skeleton */
            <StrategiesGridSkeleton count={9} />
          ) : (
            <>
              {/* Strategy Cards Grid */}
              <div className="strategy-grid">
                {strategies.length > 0 ? (
                  strategies.map((strategy) => (
                    <StrategyCard
                      key={strategy._id || strategy.id}
                      id={strategy._id || strategy.id}
                      image={strategy.thumbnailUrl ?
                        (strategy._id ?
                          getProxyUrlWithAuth(getProxyThumbnailUrl(strategy._id)) :
                          (strategy.thumbnailUrl.startsWith('http') ?
                            strategy.thumbnailUrl :
                            `${IMAGE_BASE_URL}${strategy.thumbnailUrl}`)) :
                        'https://via.placeholder.com/300x200?text=No+Image'
                      }
                      title={strategy.title}
                      coach={
                        strategy.seller?.firstName && strategy.seller?.lastName ?
                          `${strategy.seller.firstName} ${strategy.seller.lastName}` :
                          strategy.coachName || 'Unknown Coach'
                      }
                      price={
                        strategy.saleType === "Auction" && strategy.auctionDetails?.basePrice ?
                          strategy.auctionDetails.basePrice :
                          strategy.price || 0
                      }
                      contentType={strategy.contentType}
                      saleType={strategy.saleType}
                      auctionDetails={strategy.auctionDetails}
                    />
                  ))
                ) : (
                  <div className="empty-state">
                    <h3>No content found</h3>
                    <p>
                      {selectedSport !== "All" || Object.values(checkedItems).some(filterGroup =>
                        Object.values(filterGroup).some(checked => checked)
                      ) ?
                        "Try adjusting your filters or search terms." :
                        "No content available at the moment."
                      }
                    </p>
                    <button className="clear-filters-btn" onClick={clearFilters}>
                      Clear All Filters
                    </button>
                  </div>
                )}
              </div>

              {/* Pagination */}
              {strategies.length > 0 && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(pagination.total / 9)}
                  onPageChange={handlePageChange}
                  isLoading={loading.strategies}
                />
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default BuyerDashboard;


