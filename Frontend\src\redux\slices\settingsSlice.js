import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import api from '../../services/api';

// Initial state
const initialState = {
  // Public settings available to all users
  publicSettings: {
    siteName: "XOSportsHub",
    siteLogo: "",
    siteFavicon: "",
    contactEmail: "",
    contactPhone: "",
    address: "",
    supportLink: "",
    socialLinks: {
      facebook: "",
      twitter: "",
      instagram: ""
    },
    // Launch and maintenance settings
    launchDateTime: null,
    maintenanceMode: false,
    restrictionStatus: {
      restricted: false,
      reason: null,
      launchDate: null
    }
  },
  // Platform commission data
  platformCommission: {
    platformCommission: 5,
    sellerEarnings: 95,
  },
  // Loading states
  loading: {
    publicSettings: false,
    platformCommission: false,
  },
  // Error states
  errors: {
    publicSettings: null,
    platformCommission: null,
  },
  // Last updated timestamps
  lastUpdated: {
    publicSettings: null,
    platformCommission: null,
  },
};

// Async thunks
export const fetchPublicSettings = createAsyncThunk(
  'settings/fetchPublicSettings',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/settings/public');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch public settings');
    }
  }
);

export const fetchPlatformCommission = createAsyncThunk(
  'settings/fetchPlatformCommission',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/settings/platform-commission');
      return response.data.data;
    } catch (error) {
      return rejectWithValue(error.response?.data?.message || 'Failed to fetch platform commission');
    }
  }
);

// Settings slice
const settingsSlice = createSlice({
  name: 'settings',
  initialState,
  reducers: {
    // Clear errors
    clearErrors: (state) => {
      state.errors.publicSettings = null;
      state.errors.platformCommission = null;
    },
    // Reset settings to initial state
    resetSettings: (state) => {
      return initialState;
    },
    // Update public settings locally (for real-time updates)
    updatePublicSettingsLocal: (state, action) => {
      state.publicSettings = { ...state.publicSettings, ...action.payload };
      state.lastUpdated.publicSettings = new Date().toISOString();
    },
    // Update platform commission locally
    updatePlatformCommissionLocal: (state, action) => {
      state.platformCommission = { ...state.platformCommission, ...action.payload };
      state.lastUpdated.platformCommission = new Date().toISOString();
    },
  },
  extraReducers: (builder) => {
    // Fetch public settings
    builder
      .addCase(fetchPublicSettings.pending, (state) => {
        state.loading.publicSettings = true;
        state.errors.publicSettings = null;
      })
      .addCase(fetchPublicSettings.fulfilled, (state, action) => {
        state.loading.publicSettings = false;
        state.publicSettings = action.payload;
        state.lastUpdated.publicSettings = new Date().toISOString();
      })
      .addCase(fetchPublicSettings.rejected, (state, action) => {
        state.loading.publicSettings = false;
        state.errors.publicSettings = action.payload;
      });

    // Fetch platform commission
    builder
      .addCase(fetchPlatformCommission.pending, (state) => {
        state.loading.platformCommission = true;
        state.errors.platformCommission = null;
      })
      .addCase(fetchPlatformCommission.fulfilled, (state, action) => {
        state.loading.platformCommission = false;
        state.platformCommission = action.payload;
        state.lastUpdated.platformCommission = new Date().toISOString();
      })
      .addCase(fetchPlatformCommission.rejected, (state, action) => {
        state.loading.platformCommission = false;
        state.errors.platformCommission = action.payload;
      });
  },
});

// Export actions
export const {
  clearErrors,
  resetSettings,
  updatePublicSettingsLocal,
  updatePlatformCommissionLocal,
} = settingsSlice.actions;

// Selectors
export const selectPublicSettings = (state) => state.settings.publicSettings;
export const selectPlatformCommission = (state) => state.settings.platformCommission;
export const selectSettingsLoading = (state) => state.settings.loading;
export const selectSettingsErrors = (state) => state.settings.errors;
export const selectLastUpdated = (state) => state.settings.lastUpdated;

// Computed selectors
export const selectSiteName = (state) => state.settings.publicSettings.siteName;
export const selectSiteLogo = (state) => state.settings.publicSettings.siteLogo;
export const selectSiteFavicon = (state) => state.settings.publicSettings.siteFavicon;
export const selectContactEmail = (state) => state.settings.publicSettings.contactEmail;
export const selectContactPhone = (state) => state.settings.publicSettings.contactPhone;
export const selectAddress = (state) => state.settings.publicSettings.address;
export const selectSupportLink = (state) => state.settings.publicSettings.supportLink;
export const selectSocialLinks = (state) => state.settings.publicSettings.socialLinks;
export const selectMaintenanceMode = (state) => state.settings.publicSettings.maintenanceMode;
export const selectLaunchDateTime = (state) => state.settings.publicSettings.launchDateTime;
export const selectRestrictionStatus = (state) => state.settings.publicSettings.restrictionStatus;
export const selectIsRestricted = (state) => state.settings.publicSettings.restrictionStatus?.restricted || false;
export const selectRestrictionReason = (state) => state.settings.publicSettings.restrictionStatus?.reason;
export const selectLaunchDate = (state) => state.settings.publicSettings.restrictionStatus?.launchDate;
export const selectPlatformCommissionRate = (state) => state.settings.platformCommission.platformCommission;
export const selectSellerEarningsRate = (state) => state.settings.platformCommission.sellerEarnings;

export default settingsSlice.reducer;
