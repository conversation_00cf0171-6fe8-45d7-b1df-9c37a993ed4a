const mongoose = require('mongoose');

const FAQSchema = new mongoose.Schema({
  question: {
    type: String,
    required: [true, 'Please add a question'],
    trim: true,
    maxlength: [500, 'Question cannot be more than 500 characters']
  },
  answer: {
    type: String,
    required: [true, 'Please add an answer'],
    trim: true,
    maxlength: [2000, 'Answer cannot be more than 2000 characters']
  },
  order: {
    type: Number,
    default: 0,
    min: [0, 'Order cannot be negative']
  },
  status: {
    type: String,
    enum: ['active', 'inactive'],
    default: 'active'
  },
  category: {
    type: String,
    trim: true,
    maxlength: [100, 'Category cannot be more than 100 characters'],
    default: 'general'
  },
  createdBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.ObjectId,
    ref: 'User'
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date
  }
});

// Create index for efficient querying
FAQSchema.index({ status: 1, order: 1 });
FAQSchema.index({ category: 1, status: 1 });

// Update the updatedAt field before saving
FAQSchema.pre('save', function(next) {
  if (this.isModified('question') || this.isModified('answer') || this.isModified('status') || this.isModified('order')) {
    this.updatedAt = Date.now();
  }
  next();
});

// Static method to get next order number
FAQSchema.statics.getNextOrder = async function() {
  const lastFAQ = await this.findOne().sort({ order: -1 });
  return lastFAQ ? lastFAQ.order + 1 : 1;
};

module.exports = mongoose.model('FAQ', FAQSchema);
